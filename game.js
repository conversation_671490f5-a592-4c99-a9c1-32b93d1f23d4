// 蓝图连接游戏 - 核心游戏逻辑
const gameState = {
    day: 1,
    selectedTool: 'select',
    mode: 'puzzle', // 'puzzle', 'tetris', 'infinite'
    
    // Node 相关
    temporaryNodes: [], // 临时区域的节点
    placedNodes: [], // 摆放区域的节点
    connections: [], // 连接关系
    
    // Port 类型定义
    portTypes: ['square', 'diamond', 'triangle', 'circle'],
    portColors: ['#ff5252', '#2196F3', '#4CAF50', '#FFC107'],
    
    // 游戏区域设置和Canvas引用
    gridSize: 20,
    temporaryArea: { 
        x: 0, y: 0, width: 300, height: 600,
        canvas: null, ctx: null,
        maxNodes: 6  // 临时区最大节点数限制
    },
    placementArea: { 
        x: 0, y: 0, width: 700, height: 600,
        canvas: null, ctx: null 
    },
    
    // 交互状态
    draggingNode: null,
    draggingConnection: null,
    selectedNode: null,
    dragPreview: null, // 拖拽预览状态
    
    // 游戏状态
    isPlaying: false,
    score: 0,
    level: 1,
    message: '',
    
    // 关卡系统
    levelConfig: {
        maxLevel: 20,
        currentLevelStartTime: null,
        levelTimeLimit: 300, // 5分钟时间限制 (仅tetris模式)
        completedLevels: new Set(),
        levelProgress: {
            score: 0,
            totalTime: 0,
            hintsUsed: 0
        }
    },
    
    // 难度配置 - 支持多端口和多解
    difficultySettings: {
        1: { 
            level: 1, nodes: 1, chains: 1, types: 2, colors: 2, duplicateNodes: false, 
            maxPortsPerNode: 1, availableTypes: ['square', 'circle'], 
            availableColors: ['#ff5252', '#2196F3'], multipleSolutions: false 
        },
        2: { 
            level: 2, nodes: 2, chains: 1, types: 2, colors: 2, duplicateNodes: false, 
            maxPortsPerNode: 2, availableTypes: ['square', 'circle'], 
            availableColors: ['#ff5252', '#2196F3'], multipleSolutions: false 
        },
        3: { 
            level: 3, nodes: 2, chains: 2, types: 2, colors: 2, duplicateNodes: false, 
            maxPortsPerNode: 2, availableTypes: ['square', 'circle'], 
            availableColors: ['#ff5252', '#2196F3'], multipleSolutions: true 
        },
        4: { 
            level: 4, nodes: 3, chains: 2, types: 3, colors: 2, duplicateNodes: true, 
            maxPortsPerNode: 2, availableTypes: ['square', 'circle', 'triangle'], 
            availableColors: ['#ff5252', '#2196F3'], multipleSolutions: true 
        },
        5: { 
            level: 5, nodes: 3, chains: 3, types: 3, colors: 3, duplicateNodes: true, 
            maxPortsPerNode: 3, availableTypes: ['square', 'circle', 'triangle'], 
            availableColors: ['#ff5252', '#2196F3', '#4CAF50'], multipleSolutions: true 
        },
        6: { 
            level: 6, nodes: 4, chains: 3, types: 3, colors: 3, duplicateNodes: true, 
            maxPortsPerNode: 3, availableTypes: ['square', 'circle', 'triangle'], 
            availableColors: ['#ff5252', '#2196F3', '#4CAF50'], multipleSolutions: true 
        },
        7: { 
            level: 7, nodes: 4, chains: 4, types: 4, colors: 3, duplicateNodes: true, 
            maxPortsPerNode: 3, availableTypes: ['square', 'circle', 'triangle', 'diamond'], 
            availableColors: ['#ff5252', '#2196F3', '#4CAF50'], multipleSolutions: true 
        },
        8: { 
            level: 8, nodes: 5, chains: 4, types: 4, colors: 4, duplicateNodes: true, 
            maxPortsPerNode: 3, availableTypes: ['square', 'circle', 'triangle', 'diamond'], 
            availableColors: ['#ff5252', '#2196F3', '#4CAF50', '#FFC107'], multipleSolutions: true 
        },
        9: { 
            level: 9, nodes: 6, chains: 5, types: 4, colors: 4, duplicateNodes: true, 
            maxPortsPerNode: 4, availableTypes: ['square', 'circle', 'triangle', 'diamond'], 
            availableColors: ['#ff5252', '#2196F3', '#4CAF50', '#FFC107'], multipleSolutions: true 
        },
        10: { 
            level: 10, nodes: 7, chains: 6, types: 4, colors: 4, duplicateNodes: true, 
            maxPortsPerNode: 4, availableTypes: ['square', 'circle', 'triangle', 'diamond'], 
            availableColors: ['#ff5252', '#2196F3', '#4CAF50', '#FFC107'], multipleSolutions: true 
        }
    },
    
    // Tetris模式特有状态
    tetrisMode: {
        nodeQueue: [],
        nextNodePreview: null,
        dropInterval: 8000, // 8秒掉落一个节点 (调整为更合理的间隔)
        lastDropTime: 0,
        isActive: false,
        clearedLines: 0,
        combo: 0,
        timeRemaining: 0,
        overflowWarnings: 0,
        maxOverflowWarnings: 3  // 超过3次溢出警告则游戏结束
    },
    
    // Infinite无限构筑模式特有状态
    infiniteMode: {
        isActive: false,
        currentWave: 1,
        maxWaves: 10,
        baseBlueprint: null,        // 基础蓝图（从上一轮保留）
        currentRequirements: [],    // 当前波次的新要求
        completedWaves: 0,
        waveChangeInterval: 30000,  // 30秒一个波次
        lastWaveChangeTime: 0,
        accumulatedScore: 0,
        continuousSolutions: 0,     // 连续成功解决的波次数
        blueprintHistory: [],       // 蓝图历史记录
        adaptiveDifficulty: 1       // 自适应难度级别
    },
    
    // 多起终点设置
    multiNodeConfig: {
        enableMultipleStarts: false,
        enableMultipleEnds: false,
        maxStartNodes: 3,
        maxEndNodes: 3,
        requireAllPathsComplete: true, // 是否要求所有路径都完成
        currentStartNodes: [],        // 当前关卡的起点节点
        currentEndNodes: []           // 当前关卡的终点节点
    },
    
    // 算法和验证
    depthLevels: new Map(), // 节点深度级别
    executionOrder: [], // 执行顺序
    
    // 动画状态
    flowAnimation: null,
    flowAnimationState: {
        isPlaying: false,
        currentPaths: [],
        animationSteps: [],
        completedPaths: 0,
        totalPaths: 0,
        errors: []
    },
    
    // 解决方案相关
    currentSolution: null, // 保存当前场景的解决方案
    solutionVisible: false, // 是否显示解决方案提示
    
    soundEnabled: true,
    
    // 线路交叉检测配置
    crossingDetection: {
        enabled: true,           // 是否启用交叉检测
        showCrossPoints: false,  // 是否显示交叉点
        crossPoints: [],         // 存储检测到的交叉点
        allowCrossing: false     // 是否允许线路交叉
    }
};

// Port 类定义
class Port {
    constructor(type, color, side, nodeId) {
        this.id = `port_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        this.type = type; // 'square', 'diamond', 'triangle', 'circle'
        this.color = color;
        this.side = side; // 'input' (left) or 'output' (right)
        this.nodeId = nodeId;
        this.connectedTo = null; // 连接到的其他port ID
        this.x = 0;
        this.y = 0;
    }
    
    canConnectTo(otherPort) {
        return this.type === otherPort.type && 
               this.color === otherPort.color &&
               this.side !== otherPort.side &&
               this.nodeId !== otherPort.nodeId;
    }
    
    disconnect() {
        if (this.connectedTo) {
            const otherPort = findPortById(this.connectedTo);
            if (otherPort) {
                otherPort.connectedTo = null;
            }
            this.connectedTo = null;
        }
    }
}

// Node 类定义
class Node {
    constructor(type = 'normal') {
        this.id = `node_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        this.type = type; // 'start', 'end', 'normal'
        this.x = 0;
        this.y = 0;
        this.width = 120;
        this.height = 60;
        this.inputPorts = [];
        this.outputPorts = [];
        this.depth = -1; // 执行深度，-1表示未计算
        this.label = this.generateLabel();
    }
    
    generateLabel() {
        switch(this.type) {
            case 'start': return 'Start';
            case 'end': return 'End';
            default: return `Node ${Math.floor(Math.random() * 1000)}`;
        }
    }
    
    addInputPort(type, color) {
        if (this.type === 'start') return; // 起点不能有输入端口
        const port = new Port(type, color, 'input', this.id);
        this.inputPorts.push(port);
        this.updatePortPositions();
        return port;
    }
    
    addOutputPort(type, color) {
        if (this.type === 'end') return; // 终点不能有输出端口
        const port = new Port(type, color, 'output', this.id);
        this.outputPorts.push(port);
        this.updatePortPositions();
        return port;
    }
    
    updatePortPositions() {
        // 更新输入端口位置
        this.inputPorts.forEach((port, index) => {
            port.x = this.x - 8; // 稍微突出左侧
            port.y = this.y + 20 + index * 20;
        });
        
        // 更新输出端口位置
        this.outputPorts.forEach((port, index) => {
            port.x = this.x + this.width + 8; // 稍微突出右侧
            port.y = this.y + 20 + index * 20;
        });
    }
    
    getAllPorts() {
        return [...this.inputPorts, ...this.outputPorts];
    }
    
    moveTo(x, y) {
        this.x = x;
        this.y = y;
        this.updatePortPositions();
    }
    
    clone() {
        const newNode = new Node(this.type);
        newNode.label = this.label;
        
        // 复制端口
        this.inputPorts.forEach(port => {
            newNode.addInputPort(port.type, port.color);
        });
        
        this.outputPorts.forEach(port => {
            newNode.addOutputPort(port.type, port.color);
        });
        
        return newNode;
    }
}

// 连接类定义
class Connection {
    constructor(fromPort, toPort) {
        this.id = `conn_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        this.fromPort = fromPort;
        this.toPort = toPort;
        this.isValid = true;
        this.color = fromPort.color;
        this.highlighted = false;  // 是否高亮显示
        
        // 建立双向连接
        fromPort.connectedTo = toPort.id;
        toPort.connectedTo = fromPort.id;
    }
    
    disconnect() {
        this.fromPort.disconnect();
        this.toPort.disconnect();
        
        // 从连接列表中移除
        const index = gameState.connections.findIndex(conn => conn.id === this.id);
        if (index !== -1) {
            gameState.connections.splice(index, 1);
        }
    }
    
    getStartPoint() {
        return { x: this.fromPort.x, y: this.fromPort.y };
    }
    
    getEndPoint() {
        return { x: this.toPort.x, y: this.toPort.y };
    }
}

// 辅助函数：通过ID查找端口
function findPortById(portId) {
    for (const node of [...gameState.temporaryNodes, ...gameState.placedNodes]) {
        const port = node.getAllPorts().find(p => p.id === portId);
        if (port) return port;
    }
    return null;
}

// 辅助函数：通过ID查找节点
function findNodeById(nodeId) {
    return [...gameState.temporaryNodes, ...gameState.placedNodes].find(node => node.id === nodeId);
}

// 辅助函数：获取节点的所有连接
function getNodeConnections(node) {
    return gameState.connections.filter(conn => 
        conn.fromPort.nodeId === node.id || conn.toPort.nodeId === node.id
    );
}

// 智能生成可解节点
function generateRandomNode() {
    const node = new Node('normal');
    
    // 获取当前场景中需要的端口类型
    const { availableInputTypes, availableOutputTypes } = getRequiredPortTypes();
    
    // 如果没有可用类型，使用默认生成
    if (availableInputTypes.length === 0 && availableOutputTypes.length === 0) {
        return generateDefaultRandomNode();
    }
    
    // 智能生成输入端口（从可用输出类型中选择）
    const numInputs = Math.floor(Math.random() * 2) + 1; // 1-2个输入
    const inputTypes = [];
    for (let i = 0; i < numInputs && availableOutputTypes.length > 0; i++) {
        const randomType = availableOutputTypes[Math.floor(Math.random() * availableOutputTypes.length)];
        inputTypes.push(randomType);
        node.addInputPort(randomType.type, randomType.color);
    }
    
    // 智能生成输出端口（向可用输入类型提供）
    const numOutputs = Math.floor(Math.random() * 2) + 1; // 1-2个输出
    for (let i = 0; i < numOutputs; i++) {
        // 50%概率使用需要的类型，50%概率随机生成
        if (Math.random() < 0.5 && availableInputTypes.length > 0) {
            const randomType = availableInputTypes[Math.floor(Math.random() * availableInputTypes.length)];
            node.addOutputPort(randomType.type, randomType.color);
        } else {
            // 也可能是输入类型的转换输出
            if (inputTypes.length > 0 && Math.random() < 0.7) {
                const inputType = inputTypes[Math.floor(Math.random() * inputTypes.length)];
                node.addOutputPort(inputType.type, inputType.color);
            } else {
                // 完全随机
                const typeIndex = Math.floor(Math.random() * gameState.portTypes.length);
                const colorIndex = Math.floor(Math.random() * gameState.portColors.length);
                node.addOutputPort(gameState.portTypes[typeIndex], gameState.portColors[colorIndex]);
            }
        }
    }
    
    return node;
}

// 获取当前场景需要的端口类型
function getRequiredPortTypes() {
    const startNodes = gameState.placedNodes.filter(node => node.type === 'start');
    const endNodes = gameState.placedNodes.filter(node => node.type === 'end');
    const normalNodes = gameState.placedNodes.filter(node => node.type === 'normal');
    
    // 可用的输出类型（起点输出 + 普通节点输出）
    const availableOutputTypes = [];
    [...startNodes, ...normalNodes].forEach(node => {
        node.outputPorts.forEach(port => {
            availableOutputTypes.push({ type: port.type, color: port.color });
        });
    });
    
    // 需要的输入类型（终点输入 + 普通节点输入）
    const availableInputTypes = [];
    [...endNodes, ...normalNodes].forEach(node => {
        node.inputPorts.forEach(port => {
            // 检查是否已经连接
            const isConnected = gameState.connections.some(conn => conn.toPort.id === port.id);
            if (!isConnected) {
                availableInputTypes.push({ type: port.type, color: port.color });
            }
        });
    });
    
    return { availableInputTypes, availableOutputTypes };
}

// 默认随机节点生成（兜底方案）
function generateDefaultRandomNode() {
    const node = new Node('normal');
    
    // 随机生成1-2个输入端口
    const numInputs = Math.floor(Math.random() * 2) + 1;
    for (let i = 0; i < numInputs; i++) {
        const typeIndex = Math.floor(Math.random() * gameState.portTypes.length);
        const colorIndex = Math.floor(Math.random() * gameState.portColors.length);
        node.addInputPort(gameState.portTypes[typeIndex], gameState.portColors[colorIndex]);
    }
    
    // 随机生成1-2个输出端口
    const numOutputs = Math.floor(Math.random() * 2) + 1;
    for (let i = 0; i < numOutputs; i++) {
        const typeIndex = Math.floor(Math.random() * gameState.portTypes.length);
        const colorIndex = Math.floor(Math.random() * gameState.portColors.length);
        node.addOutputPort(gameState.portTypes[typeIndex], gameState.portColors[colorIndex]);
    }
    
    return node;
}

// 生成起点和终点节点（兼容性函数）
function generateStartEndNodes() {
    // 使用新的兼容性生成函数
    return generateCompatibleStartEndNodes();
}

// 计算节点深度和执行顺序
function calculateDepthAndOrder() {
    // 重置深度
    gameState.depthLevels.clear();
    gameState.executionOrder = [];
    
    // 找到所有起点
    const startNodes = gameState.placedNodes.filter(node => node.type === 'start');
    
    // BFS 计算深度
    const queue = startNodes.map(node => ({ node, depth: 0 }));
    const visited = new Set();
    
    while (queue.length > 0) {
        const { node, depth } = queue.shift();
        
        if (visited.has(node.id)) continue;
        visited.add(node.id);
        
        node.depth = depth;
        
        if (!gameState.depthLevels.has(depth)) {
            gameState.depthLevels.set(depth, []);
        }
        gameState.depthLevels.get(depth).push(node);
        
        // 找到所有输出连接
        const outputConnections = gameState.connections.filter(conn => 
            conn.fromPort.nodeId === node.id
        );
        
        for (const conn of outputConnections) {
            const targetNode = findNodeById(conn.toPort.nodeId);
            if (targetNode && !visited.has(targetNode.id)) {
                queue.push({ node: targetNode, depth: depth + 1 });
            }
        }
    }
    
    // 生成执行顺序
    const maxDepth = Math.max(...gameState.depthLevels.keys());
    for (let depth = 0; depth <= maxDepth; depth++) {
        if (gameState.depthLevels.has(depth)) {
            gameState.executionOrder.push(...gameState.depthLevels.get(depth));
        }
    }
}

// 检查是否有环
function hasCycle() {
    const visited = new Set();
    const recursionStack = new Set();
    
    function dfs(nodeId) {
        if (recursionStack.has(nodeId)) return true; // 发现环
        if (visited.has(nodeId)) return false;
        
        visited.add(nodeId);
        recursionStack.add(nodeId);
        
        // 检查所有输出连接
        const outputConnections = gameState.connections.filter(conn => 
            conn.fromPort.nodeId === nodeId
        );
        
        for (const conn of outputConnections) {
            if (dfs(conn.toPort.nodeId)) return true;
        }
        
        recursionStack.delete(nodeId);
        return false;
    }
    
    // 检查所有节点
    for (const node of gameState.placedNodes) {
        if (!visited.has(node.id)) {
            if (dfs(node.id)) return true;
        }
    }
    
    return false;
}

// 验证当前蓝图是否有效（支持多解）
function validateBlueprint() {
    // 检查是否是多节点场景
    if (gameState.multiNodeConfig.currentStartNodes.length > 0 || gameState.multiNodeConfig.currentEndNodes.length > 0) {
        return validateMultiPathBlueprint();
    }
    
    // 获取当前难度设置
    const currentLevel = gameState.level;
    const difficulty = gameState.difficultySettings[Math.min(currentLevel, 10)];
    
    let validationResult;
    
    if (difficulty && difficulty.multipleSolutions) {
        // 支持多解的验证
        validationResult = performMultipleSolutionsValidation();
    } else {
        // 传统单解验证
        validationResult = performDetailedValidation();
    }
    
    if (!validationResult.isValid) {
        updateMessage(`验证失败：${validationResult.errors.join(', ')}`);
        return false;
    }
    
    // 对于多解模式，如果找到任何有效解就算成功
    if (difficulty && difficulty.multipleSolutions && validationResult.solutionCount > 0) {
        const solutionCount = validationResult.solutionCount;
        updateMessage(`🎉 发现 ${solutionCount} 种解决方案！关卡完成！`);
        completeLevel();
        return true;
    }
    
    // 传统模式的解决方案检查
    const solutionCheck = validateAgainstSolution();
    if (solutionCheck.isComplete) {
        // 关卡完成
        completeLevel();
        return true;
    } else {
        updateMessage(`进度: ${solutionCheck.completedSteps}/${solutionCheck.totalSteps} 步骤完成`);
        if (validationResult.warnings.length > 0) {
            console.log('警告:', validationResult.warnings);
        }
        return false;
    }
}

// 完成关卡
function completeLevel() {
    const level = gameState.level;
    const levelConfig = gameState.levelConfig;
    
    // 计算关卡分数
    const levelScore = calculateLevelScore();
    gameState.score += levelScore;
    gameState.levelConfig.levelProgress.score += levelScore;
    
    // 标记关卡完成
    levelConfig.completedLevels.add(level);
    
    // 更新UI
    updateLevelDisplay();
    
    // 成功消息
    updateMessage(`🎉 关卡 ${level} 完成！得分: ${levelScore}分`);
    
    // 播放完成动画
    playLevelCompleteAnimation();
    
    // 延迟后自动进入下一关
    setTimeout(() => {
        advanceToNextLevel();
    }, 3000);
}

// 计算关卡分数
function calculateLevelScore() {
    const baseScore = 1000;
    const levelMultiplier = gameState.level;
    const timeBonus = gameState.levelConfig.currentLevelStartTime 
        ? Math.max(0, 60000 - (Date.now() - gameState.levelConfig.currentLevelStartTime)) / 1000
        : 0;
    const hintPenalty = gameState.levelConfig.levelProgress.hintsUsed * 50;
    
    return Math.floor(baseScore * levelMultiplier + timeBonus - hintPenalty);
}

// 进入下一关
function advanceToNextLevel() {
    const currentLevel = gameState.level;
    const maxLevel = gameState.levelConfig.maxLevel;
    
    if (currentLevel >= maxLevel) {
        // 游戏完成
        completeGame();
        return;
    }
    
    // 进入下一关
    gameState.level++;
    gameState.levelConfig.currentLevelStartTime = Date.now();
    gameState.levelConfig.levelProgress.hintsUsed = 0;
    
    // 根据游戏模式生成新关卡
    if (gameState.mode === 'puzzle') {
        generatePuzzleLevel();
    } else if (gameState.mode === 'tetris') {
        startTetrisLevel();
    }
    
    updateLevelDisplay();
    updateMessage(`🚀 欢迎来到关卡 ${gameState.level}！`);
}

// 完成游戏
function completeGame() {
    const totalScore = gameState.score;
    const totalTime = gameState.levelConfig.levelProgress.totalTime;
    
    updateMessage(`🏆 恭喜通关！总分: ${totalScore}分，用时: ${Math.floor(totalTime/60000)}分${Math.floor((totalTime%60000)/1000)}秒`);
    
    // 可以在这里添加成就系统、排行榜等功能
    console.log('🎉 游戏完成统计:', {
        totalScore,
        totalTime,
        completedLevels: Array.from(gameState.levelConfig.completedLevels),
        averageScore: Math.floor(totalScore / gameState.levelConfig.completedLevels.size)
    });
}

// 验证当前连接是否符合预设解决方案
function validateAgainstSolution() {
    // 获取当前场景的解决方案（如果存在）
    if (!gameState.currentSolution) {
        return { isComplete: false, completedSteps: 0, totalSteps: 0 };
    }
    
    const solution = gameState.currentSolution;
    let completedSteps = 0;
    const totalSteps = solution.length;
    
    // 检查每个转换链是否正确连接
    for (const chain of solution) {
        const chainCompleted = validateTransformationChainExecution(chain);
        if (chainCompleted) {
            completedSteps++;
        }
    }
    
    return {
        isComplete: completedSteps === totalSteps,
        completedSteps,
        totalSteps
    };
}

// 验证转换链的执行
function validateTransformationChainExecution(chain) {
    // 检查链是否有效
    if (!chain || !chain.source || !chain.target) {
        console.warn('Invalid transformation chain:', chain);
        return false;
    }

    // 找到起点节点（提供源数据）
    const startNodes = gameState.placedNodes.filter(node => node.type === 'start');
    const sourceNode = startNodes.find(node =>
        node.outputPorts && node.outputPorts.some(port =>
            port && port.type === chain.source.type && port.color === chain.source.color
        )
    );
    
    if (!sourceNode) return false;
    
    // 找到终点节点（需要目标数据）
    const endNodes = gameState.placedNodes.filter(node => node.type === 'end');
    const targetNode = endNodes.find(node => 
        node.inputPorts.some(port => 
            port.type === chain.target.type && port.color === chain.target.color
        )
    );
    
    if (!targetNode) return false;
    
    // 验证转换路径是否正确连接
    return validateTransformationPath(sourceNode, targetNode, chain.steps);
}

// 验证转换路径
function validateTransformationPath(sourceNode, targetNode, steps) {
    // 如果没有转换步骤，检查直接连接
    if (steps.length === 0) {
        return validateDirectConnection(sourceNode, targetNode);
    }
    
    // 有转换步骤的情况
    let currentNode = sourceNode;
    let currentOutputPort = null;
    
    // 找到起点的相关输出端口
    for (const outputPort of sourceNode.outputPorts) {
        if (outputPort.type === steps[0].inputType && outputPort.color === steps[0].inputColor) {
            if (outputPort.connectedTo) {
                currentOutputPort = outputPort;
                break;
            }
        }
    }
    
    if (!currentOutputPort) return false;
    
    // 验证每个转换步骤
    for (let i = 0; i < steps.length; i++) {
        const step = steps[i];
        
        // 找到当前输出连接的输入端口
        const nextInputPort = findPortById(currentOutputPort.connectedTo);
        if (!nextInputPort) return false;
        
        const stepNode = findNodeById(nextInputPort.nodeId);
        if (!stepNode || stepNode.type !== 'normal') return false;
        
        // 验证输入匹配
        if (nextInputPort.type !== step.inputType || nextInputPort.color !== step.inputColor) {
            return false;
        }
        
        // 找到对应的输出端口
        const stepOutputPort = stepNode.outputPorts.find(port => 
            port.type === step.outputType && port.color === step.outputColor
        );
        
        if (!stepOutputPort) return false;
        
        // 如果是最后一步，检查是否连接到目标
        if (i === steps.length - 1) {
            if (!stepOutputPort.connectedTo) return false;
            
            const finalTargetPort = findPortById(stepOutputPort.connectedTo);
            if (!finalTargetPort || finalTargetPort.nodeId !== targetNode.id) {
                return false;
            }
            
            // 验证最终目标端口类型匹配
            if (finalTargetPort.type !== step.outputType || finalTargetPort.color !== step.outputColor) {
                return false;
            }
        } else {
            // 不是最后一步，准备下一步
            if (!stepOutputPort.connectedTo) return false;
            currentOutputPort = stepOutputPort;
        }
        
        currentNode = stepNode;
    }
    
    return true;
}

// 验证直接连接
function validateDirectConnection(sourceNode, targetNode) {
    for (const outputPort of sourceNode.outputPorts) {
        if (outputPort.connectedTo) {
            const targetPort = findPortById(outputPort.connectedTo);
            if (targetPort && targetPort.nodeId === targetNode.id) {
                // 验证类型匹配
                if (outputPort.type === targetPort.type && outputPort.color === targetPort.color) {
                    return true;
                }
            }
        }
    }
    return false;
}

// 执行详细的蓝图验证
function performDetailedValidation() {
    const errors = [];
    const warnings = [];
    
    // 1. 检查环
    if (hasCycle()) {
        errors.push('存在循环依赖');
        return { isValid: false, errors, warnings };
    }
    
    // 2. 计算深度和顺序
    calculateDepthAndOrder();
    
    // 3. 检查节点连接完整性
    const nodeValidation = validateNodeConnections();
    errors.push(...nodeValidation.errors);
    warnings.push(...nodeValidation.warnings);
    
    // 4. 检查端口连接完整性
    const portValidation = validatePortConnections();
    errors.push(...portValidation.errors);
    warnings.push(...portValidation.warnings);
    
    // 5. 检查数据流完整性
    const flowValidation = validateDataFlow();
    errors.push(...flowValidation.errors);
    warnings.push(...flowValidation.warnings);
    
    return {
        isValid: errors.length === 0,
        errors: errors.filter((error, index, arr) => arr.indexOf(error) === index), // 去重
        warnings: warnings.filter((warning, index, arr) => arr.indexOf(warning) === index) // 去重
    };
}

// 多解验证 - 寻找所有可能的解决方案
function performMultipleSolutionsValidation() {
    const errors = [];
    const warnings = [];
    
    // 1. 基础验证（环检查等）
    if (hasCycle()) {
        errors.push('存在循环依赖');
        return { isValid: false, errors, warnings, solutionCount: 0 };
    }
    
    // 2. 计算深度和顺序
    calculateDepthAndOrder();
    
    // 3. 寻找所有可能的解决方案路径
    const startNodes = gameState.placedNodes.filter(node => node.type === 'start');
    const endNodes = gameState.placedNodes.filter(node => node.type === 'end');
    
    if (startNodes.length === 0) {
        errors.push('缺少起点节点');
    }
    if (endNodes.length === 0) {
        errors.push('缺少终点节点');
    }
    
    if (errors.length > 0) {
        return { isValid: false, errors, warnings, solutionCount: 0 };
    }
    
    // 4. 寻找从每个起点到每个终点的所有路径
    const allSolutions = [];
    
    for (const startNode of startNodes) {
        for (const endNode of endNodes) {
            const paths = findAllValidPaths(startNode, endNode);
            paths.forEach(path => {
                if (validateSolutionPath(path)) {
                    allSolutions.push({
                        start: startNode.id,
                        end: endNode.id,
                        path: path,
                        score: calculatePathScore(path)
                    });
                }
            });
        }
    }
    
    // 5. 验证是否至少有一个有效解
    if (allSolutions.length === 0) {
        errors.push('没有找到有效的解决方案路径');
        return { isValid: false, errors, warnings, solutionCount: 0 };
    }
    
    // 6. 检查端口连接的完整性（在多解模式下更宽松）
    const portValidation = validatePortConnections();
    warnings.push(...portValidation.warnings);
    
    // 只把严重的端口错误当作错误
    const portErrors = portValidation.errors.filter(error => 
        error.includes('连接到不存在的端口') || error.includes('连接不一致')
    );
    errors.push(...portErrors);
    
    return {
        isValid: errors.length === 0,
        errors: errors.filter((error, index, arr) => arr.indexOf(error) === index),
        warnings: warnings.filter((warning, index, arr) => arr.indexOf(warning) === index),
        solutionCount: allSolutions.length,
        solutions: allSolutions
    };
}

// 寻找两个节点之间的所有有效路径
function findAllValidPaths(startNode, endNode, visited = new Set(), currentPath = []) {
    const paths = [];
    
    // 避免无限循环
    if (visited.has(startNode.id)) {
        return paths;
    }
    
    // 添加当前节点到路径
    const newPath = [...currentPath, startNode];
    const newVisited = new Set([...visited, startNode.id]);
    
    // 如果到达终点，返回路径
    if (startNode.id === endNode.id) {
        paths.push(newPath);
        return paths;
    }
    
    // 获取所有可连接的下一个节点
    const connectedNodes = getConnectedNodes(startNode);
    
    for (const nextNode of connectedNodes) {
        if (!visited.has(nextNode.id)) {
            const subPaths = findAllValidPaths(nextNode, endNode, newVisited, newPath);
            paths.push(...subPaths);
        }
    }
    
    return paths;
}

// 验证单个解决方案路径的有效性
function validateSolutionPath(path) {
    if (path.length < 2) return false;
    
    for (let i = 0; i < path.length - 1; i++) {
        const currentNode = path[i];
        const nextNode = path[i + 1];
        
        // 检查节点之间是否有有效连接
        const connection = findConnectionBetweenNodes(currentNode, nextNode);
        if (!connection) {
            return false;
        }
        
        // 检查端口类型匹配
        if (!connection.fromPort.canConnectTo(connection.toPort)) {
            return false;
        }
    }
    
    return true;
}

// 计算路径得分（用于排序不同解决方案）
function calculatePathScore(path) {
    let score = 0;
    
    // 路径长度得分（较短路径得分更高）
    score += Math.max(0, 10 - path.length);
    
    // 节点多样性得分
    const nodeTypes = new Set(path.map(node => node.type));
    score += nodeTypes.size * 2;
    
    // 端口利用率得分
    let totalPorts = 0;
    let usedPorts = 0;
    
    path.forEach(node => {
        totalPorts += node.getAllPorts().length;
        usedPorts += node.getAllPorts().filter(port => port.connectedTo).length;
    });
    
    if (totalPorts > 0) {
        score += (usedPorts / totalPorts) * 5;
    }
    
    return score;
}

// 验证节点连接
function validateNodeConnections() {
    const errors = [];
    const warnings = [];
    
    const startNodes = gameState.placedNodes.filter(node => node.type === 'start');
    const endNodes = gameState.placedNodes.filter(node => node.type === 'end');
    
    // 检查是否有起点和终点
    if (startNodes.length === 0) {
        errors.push('缺少起点节点');
    }
    if (endNodes.length === 0) {
        errors.push('缺少终点节点');
    }
    
    // 检查所有终点是否可达
    for (const endNode of endNodes) {
        let reachable = false;
        for (const startNode of startNodes) {
            if (canReach(startNode, endNode)) {
                reachable = true;
                break;
            }
        }
        if (!reachable) {
            errors.push(`终点 ${endNode.label} 无法从任何起点到达`);
        }
    }
    
    // 检查孤立节点
    const connectedNodes = new Set();
    gameState.connections.forEach(conn => {
        connectedNodes.add(conn.fromPort.nodeId);
        connectedNodes.add(conn.toPort.nodeId);
    });
    
    gameState.placedNodes.forEach(node => {
        if (node.type === 'normal' && !connectedNodes.has(node.id)) {
            warnings.push(`节点 ${node.label} 没有任何连接`);
        }
    });
    
    return { errors, warnings };
}

// 验证端口连接
function validatePortConnections() {
    const errors = [];
    const warnings = [];
    
    const startNodes = gameState.placedNodes.filter(node => node.type === 'start');
    const endNodes = gameState.placedNodes.filter(node => node.type === 'end');
    
    // 验证一对一连接约束
    const oneToOneValidation = validateRuntimeOneToOneConstraint();
    errors.push(...oneToOneValidation.errors);
    warnings.push(...oneToOneValidation.warnings);
    
    // 检查起点的输出端口是否都有连接
    startNodes.forEach(startNode => {
        startNode.outputPorts.forEach(port => {
            const hasConnection = gameState.connections.some(conn => conn.fromPort.id === port.id);
            if (!hasConnection) {
                warnings.push(`起点 ${startNode.label} 的输出端口未连接`);
            }
        });
    });
    
    // 检查终点的输入端口是否都有连接
    endNodes.forEach(endNode => {
        const connectedInputs = endNode.inputPorts.filter(port => 
            gameState.connections.some(conn => conn.toPort.id === port.id)
        );
        
        if (connectedInputs.length === 0) {
            errors.push(`终点 ${endNode.label} 没有任何输入连接`);
        } else if (connectedInputs.length < endNode.inputPorts.length) {
            const unconnectedCount = endNode.inputPorts.length - connectedInputs.length;
            warnings.push(`终点 ${endNode.label} 有 ${unconnectedCount} 个输入端口未连接`);
        }
    });
    
    // 检查中间节点的连接
    const normalNodes = gameState.placedNodes.filter(node => node.type === 'normal');
    normalNodes.forEach(node => {
        const connectedInputs = node.inputPorts.filter(port => 
            gameState.connections.some(conn => conn.toPort.id === port.id)
        );
        const connectedOutputs = node.outputPorts.filter(port => 
            gameState.connections.some(conn => conn.fromPort.id === port.id)
        );
        
        if (connectedInputs.length === 0) {
            warnings.push(`节点 ${node.label} 没有输入连接`);
        }
        if (connectedOutputs.length === 0) {
            warnings.push(`节点 ${node.label} 没有输出连接`);
        }
    });
    
    return { errors, warnings };
}

// 验证运行时一对一连接约束
function validateRuntimeOneToOneConstraint() {
    const errors = [];
    const warnings = [];
    
    // 检查每个端口的连接数量
    const allNodes = [...gameState.temporaryNodes, ...gameState.placedNodes];
    const portConnectionCount = new Map();
    
    // 统计每个端口的连接数
    gameState.connections.forEach(connection => {
        const fromPortKey = connection.fromPort.id;
        const toPortKey = connection.toPort.id;
        
        portConnectionCount.set(fromPortKey, (portConnectionCount.get(fromPortKey) || 0) + 1);
        portConnectionCount.set(toPortKey, (portConnectionCount.get(toPortKey) || 0) + 1);
    });
    
    // 检查是否有端口违反一对一约束
    allNodes.forEach(node => {
        node.getAllPorts().forEach(port => {
            const connectionCount = portConnectionCount.get(port.id) || 0;
            
            if (connectionCount > 1) {
                errors.push(`端口 ${port.type}(${port.color}) 在节点 ${node.label} 上有 ${connectionCount} 个连接，违反一对一约束`);
            }
            
            // 验证 connectedTo 字段的一致性
            if (port.connectedTo) {
                const otherPort = findPortById(port.connectedTo);
                if (!otherPort) {
                    errors.push(`端口 ${port.id} 连接到不存在的端口 ${port.connectedTo}`);
                } else if (otherPort.connectedTo !== port.id) {
                    errors.push(`端口连接不一致: ${port.id} 连接到 ${port.connectedTo}，但反向连接不匹配`);
                }
            }
        });
    });
    
    // 检查连接对象与端口状态的一致性
    gameState.connections.forEach(connection => {
        if (connection.fromPort.connectedTo !== connection.toPort.id) {
            errors.push(`连接对象不一致: fromPort.connectedTo=${connection.fromPort.connectedTo}, toPort.id=${connection.toPort.id}`);
        }
        if (connection.toPort.connectedTo !== connection.fromPort.id) {
            errors.push(`连接对象不一致: toPort.connectedTo=${connection.toPort.connectedTo}, fromPort.id=${connection.fromPort.id}`);
        }
    });
    
    return { errors, warnings };
}

// 验证数据流
function validateDataFlow() {
    const errors = [];
    const warnings = [];
    
    const startNodes = gameState.placedNodes.filter(node => node.type === 'start');
    const endNodes = gameState.placedNodes.filter(node => node.type === 'end');
    
    // 检查每个起点到终点的数据流
    for (const startNode of startNodes) {
        for (const endNode of endNodes) {
            const flowPaths = findDataFlowPaths(startNode, endNode);
            if (flowPaths.length === 0) {
                // 这已经在节点连接检查中覆盖了
                continue;
            }
            
            // 检查数据流的完整性
            for (const path of flowPaths) {
                const flowValidation = validateDataFlowPath(path);
                errors.push(...flowValidation.errors);
                warnings.push(...flowValidation.warnings);
            }
        }
    }
    
    return { errors, warnings };
}

// 查找数据流路径
function findDataFlowPaths(startNode, endNode) {
    const paths = [];
    const visited = new Set();
    
    function dfs(currentNode, currentPath) {
        if (visited.has(currentNode.id)) return;
        visited.add(currentNode.id);
        
        const newPath = [...currentPath, currentNode];
        
        if (currentNode.id === endNode.id) {
            paths.push(newPath);
            visited.delete(currentNode.id);
            return;
        }
        
        // 找到所有输出连接
        const outputConnections = gameState.connections.filter(conn => 
            conn.fromPort.nodeId === currentNode.id
        );
        
        for (const conn of outputConnections) {
            const nextNode = findNodeById(conn.toPort.nodeId);
            if (nextNode && !visited.has(nextNode.id)) {
                dfs(nextNode, newPath);
            }
        }
        
        visited.delete(currentNode.id);
    }
    
    dfs(startNode, []);
    return paths;
}

// 验证单个数据流路径
function validateDataFlowPath(path) {
    const errors = [];
    const warnings = [];
    
    // 检查路径中相邻节点间的连接
    for (let i = 0; i < path.length - 1; i++) {
        const fromNode = path[i];
        const toNode = path[i + 1];
        
        // 找到这两个节点间的连接
        const connection = gameState.connections.find(conn => 
            conn.fromPort.nodeId === fromNode.id && conn.toPort.nodeId === toNode.id
        );
        
        if (!connection) {
            errors.push(`节点 ${fromNode.label} 和 ${toNode.label} 之间缺少连接`);
        } else {
            // 检查端口类型匹配
            if (connection.fromPort.type !== connection.toPort.type || 
                connection.fromPort.color !== connection.toPort.color) {
                errors.push(`节点 ${fromNode.label} 和 ${toNode.label} 之间的端口类型不匹配`);
            }
        }
    }
    
    return { errors, warnings };
}

// 检查是否可以从一个节点到达另一个节点
function canReach(fromNode, toNode) {
    const visited = new Set();
    const queue = [fromNode];
    
    while (queue.length > 0) {
        const node = queue.shift();
        if (visited.has(node.id)) continue;
        visited.add(node.id);
        
        if (node.id === toNode.id) return true;
        
        // 添加所有输出连接的目标节点
        const outputConnections = gameState.connections.filter(conn => 
            conn.fromPort.nodeId === node.id
        );
        
        for (const conn of outputConnections) {
            const targetNode = findNodeById(conn.toPort.nodeId);
            if (targetNode && !visited.has(targetNode.id)) {
                queue.push(targetNode);
            }
        }
    }
    
    return false;
}

// 播放流动动画
function playFlowAnimation() {
    // 如果已经在播放，先停止
    if (gameState.flowAnimationState.isPlaying) {
        stopFlowAnimation();
    }
    
    // 重置动画状态
    resetFlowAnimationState();
    
    // 获取所有起点节点
    const startNodes = gameState.placedNodes.filter(node => node.type === 'start');
    if (startNodes.length === 0) {
        updateMessage('❌ 没有找到起点节点！');
        return;
    }
    
    // 获取所有终点节点
    const endNodes = gameState.placedNodes.filter(node => node.type === 'end');
    if (endNodes.length === 0) {
        updateMessage('❌ 没有找到终点节点！');
        return;
    }
    
    // 为每个起点到终点找到路径
    const allPaths = [];
    const pathErrors = [];
    
    for (const startNode of startNodes) {
        for (const endNode of endNodes) {
            const paths = findAllPathsFromStartToEnd(startNode, endNode);
            if (paths.length === 0) {
                pathErrors.push(`无法从起点 ${startNode.id} 到达终点 ${endNode.id}`);
            } else {
                allPaths.push(...paths);
            }
        }
    }
    
    // 检查是否有路径错误
    if (pathErrors.length > 0) {
        updateMessage(`❌ 路径错误: ${pathErrors.join(', ')}`);
        return;
    }
    
    if (allPaths.length === 0) {
        updateMessage('❌ 没有找到有效的连接路径！');
        return;
    }
    
    // 启动流动动画
    gameState.flowAnimationState.isPlaying = true;
    gameState.flowAnimationState.currentPaths = allPaths;
    gameState.flowAnimationState.totalPaths = allPaths.length;
    gameState.flowAnimationState.completedPaths = 0;
    gameState.flowAnimationState.errors = [];
    
    updateMessage(`🎬 开始播放流动动画... (${allPaths.length} 条路径)`);
    
    // 同时播放所有路径的动画
    allPaths.forEach((path, index) => {
        playPathAnimation(path, index);
    });
}

// 重置流动动画状态
function resetFlowAnimationState() {
    gameState.flowAnimationState = {
        isPlaying: false,
        currentPaths: [],
        animationSteps: [],
        completedPaths: 0,
        totalPaths: 0,
        errors: []
    };
}

// 停止流动动画
function stopFlowAnimation() {
    if (gameState.flowAnimation) {
        clearInterval(gameState.flowAnimation);
        gameState.flowAnimation = null;
    }
    resetFlowAnimationState();
}

// 播放单条路径的动画
function playPathAnimation(path, pathIndex) {
    if (!path || path.length === 0) return;
    
    let currentStep = 0;
    const animationInterval = 800; // 每步动画间隔
    
    const animateStep = () => {
        if (currentStep >= path.length) {
            // 路径动画完成
            onPathAnimationComplete(pathIndex);
            return;
        }
        
        const node = path[currentStep];
        
        // 检查当前节点的连接是否有效
        const nodeErrors = validateSingleNodeConnections(node, path, currentStep);
        if (nodeErrors.length > 0) {
            // 发现错误，停止动画并报告
            gameState.flowAnimationState.errors.push(...nodeErrors);
            onPathAnimationError(pathIndex, nodeErrors);
            return;
        }
        
        // 播放节点动画
        playNodeAnimation(node);
        
        // 如果不是最后一个节点，播放连接动画
        if (currentStep < path.length - 1) {
            const nextNode = path[currentStep + 1];
            const connection = findConnectionBetweenNodes(node, nextNode);
            if (connection) {
                setTimeout(() => {
                    playConnectionAnimation(connection);
                }, animationInterval / 2);
            }
        }
        
        currentStep++;
        setTimeout(animateStep, animationInterval);
    };
    
    animateStep();
}

// 播放节点动画
function playNodeAnimation(node) {
    const canvas = document.getElementById('game-canvas');
    const ctx = canvas.getContext('2d');
    
    // 创建脉冲效果
    const originalScale = 1;
    const targetScale = 1.3;
    const duration = 600;
    let startTime = Date.now();
    
    const animate = () => {
        const elapsed = Date.now() - startTime;
        const progress = Math.min(elapsed / duration, 1);
        
        // 缓动函数
        const easeInOut = t => t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t;
        const scale = originalScale + (targetScale - originalScale) * easeInOut(progress);
        
        // 重新绘制节点
        ctx.save();
        ctx.translate(node.x + node.width / 2, node.y + node.height / 2);
        ctx.scale(scale, scale);
        ctx.translate(-node.width / 2, -node.height / 2);
        
        // 绘制发光效果
        ctx.shadowColor = '#4CAF50';
        ctx.shadowBlur = 20;
        ctx.fillStyle = '#4CAF50';
        ctx.fillRect(0, 0, node.width, node.height);
        
        ctx.restore();
        
        if (progress < 1) {
            requestAnimationFrame(animate);
        }
    };
    
    animate();
}

// 播放连接动画
function playConnectionAnimation(connection) {
    const canvas = document.getElementById('game-canvas');
    const ctx = canvas.getContext('2d');
    
    const startPoint = connection.getStartPoint();
    const endPoint = connection.getEndPoint();
    
    // 创建流动粒子效果
    const particles = [];
    const particleCount = 3;
    
    for (let i = 0; i < particleCount; i++) {
        particles.push({
            progress: i / particleCount,
            speed: 0.02,
            size: 8,
            color: connection.color
        });
    }
    
    const animateParticles = () => {
        particles.forEach(particle => {
            particle.progress += particle.speed;
            
            if (particle.progress <= 1) {
                // 计算粒子位置
                const t = particle.progress;
                const x = startPoint.x + (endPoint.x - startPoint.x) * t;
                const y = startPoint.y + (endPoint.y - startPoint.y) * t;
                
                // 绘制粒子
                ctx.save();
                ctx.fillStyle = particle.color;
                ctx.shadowColor = particle.color;
                ctx.shadowBlur = 10;
                ctx.beginPath();
                ctx.arc(x, y, particle.size, 0, Math.PI * 2);
                ctx.fill();
                ctx.restore();
            }
        });
        
        // 继续动画直到所有粒子完成
        if (particles.some(p => p.progress <= 1)) {
            requestAnimationFrame(animateParticles);
        }
    };
    
    animateParticles();
}

// 验证单个节点连接 (用于动画)
function validateSingleNodeConnections(node, path, stepIndex) {
    const errors = [];
    
    // 检查节点是否存在
    if (!node) {
        errors.push(`路径中的第 ${stepIndex} 个节点不存在`);
        return errors;
    }
    
    // 检查输入连接
    const inputPorts = node.inputPorts || [];
    for (const port of inputPorts) {
        if (port.connectedTo) {
            const connectedPort = findPortById(port.connectedTo);
            if (!connectedPort) {
                errors.push(`节点 ${node.id} 的输入端口连接无效`);
            } else if (!port.canConnectTo(connectedPort)) {
                errors.push(`节点 ${node.id} 的输入端口类型不匹配`);
            }
        }
    }
    
    // 检查输出连接
    const outputPorts = node.outputPorts || [];
    for (const port of outputPorts) {
        if (port.connectedTo) {
            const connectedPort = findPortById(port.connectedTo);
            if (!connectedPort) {
                errors.push(`节点 ${node.id} 的输出端口连接无效`);
            } else if (!port.canConnectTo(connectedPort)) {
                errors.push(`节点 ${node.id} 的输出端口类型不匹配`);
            }
        }
    }
    
    return errors;
}

// 路径动画完成
function onPathAnimationComplete(pathIndex) {
    gameState.flowAnimationState.completedPaths++;
    
    console.log(`路径 ${pathIndex + 1} 动画完成`);
    
    // 检查是否所有路径都完成
    if (gameState.flowAnimationState.completedPaths >= gameState.flowAnimationState.totalPaths) {
        // 所有路径都完成，检查是否有错误
        if (gameState.flowAnimationState.errors.length === 0) {
            // 完全成功
            onFlowAnimationSuccess();
        } else {
            // 有错误
            onFlowAnimationError();
        }
    }
}

// 路径动画错误
function onPathAnimationError(pathIndex, errors) {
    console.log(`路径 ${pathIndex + 1} 动画错误:`, errors);
    
    gameState.flowAnimationState.errors.push(...errors);
    gameState.flowAnimationState.completedPaths++;
    
    // 检查是否所有路径都完成
    if (gameState.flowAnimationState.completedPaths >= gameState.flowAnimationState.totalPaths) {
        onFlowAnimationError();
    }
}

// 流动动画完全成功
function onFlowAnimationSuccess() {
    updateMessage('🎉 流动动画播放成功！蓝图验证通过！');
    
    // 播放成功动画
    playSuccessAnimation();
    
    // 延迟后自动进入下一关
    setTimeout(() => {
        completeLevel();
    }, 2000);
}

// 流动动画有错误
function onFlowAnimationError() {
    const uniqueErrors = [...new Set(gameState.flowAnimationState.errors)];
    const errorMessage = `❌ 流动动画发现错误: ${uniqueErrors.join(', ')}`;
    
    updateMessage(errorMessage);
    
    // 播放错误动画
    playErrorAnimation();
    
    // 重置动画状态
    resetFlowAnimationState();
}

// 播放成功动画
function playSuccessAnimation() {
    const canvas = document.getElementById('game-canvas');
    const ctx = canvas.getContext('2d');
    
    // 创建庆祝粒子效果
    const particles = [];
    const particleCount = 20;
    
    for (let i = 0; i < particleCount; i++) {
        particles.push({
            x: Math.random() * canvas.width,
            y: Math.random() * canvas.height,
            vx: (Math.random() - 0.5) * 4,
            vy: (Math.random() - 0.5) * 4,
            life: 1,
            color: ['#4CAF50', '#2196F3', '#FF9800', '#9C27B0'][Math.floor(Math.random() * 4)]
        });
    }
    
    const animateSuccess = () => {
        ctx.save();
        ctx.globalAlpha = 0.1;
        ctx.fillStyle = '#ffffff';
        ctx.fillRect(0, 0, canvas.width, canvas.height);
        ctx.restore();
        
        particles.forEach(particle => {
            particle.x += particle.vx;
            particle.y += particle.vy;
            particle.life -= 0.01;
            
            if (particle.life > 0) {
                ctx.save();
                ctx.globalAlpha = particle.life;
                ctx.fillStyle = particle.color;
                ctx.beginPath();
                ctx.arc(particle.x, particle.y, 5, 0, Math.PI * 2);
                ctx.fill();
                ctx.restore();
            }
        });
        
        if (particles.some(p => p.life > 0)) {
            requestAnimationFrame(animateSuccess);
        }
    };
    
    animateSuccess();
}

// 播放错误动画
function playErrorAnimation() {
    const canvas = document.getElementById('game-canvas');
    const ctx = canvas.getContext('2d');
    
    // 创建震动效果
    let shakeIntensity = 10;
    let shakeCount = 0;
    const maxShakes = 10;
    
    const shakeAnimation = () => {
        if (shakeCount >= maxShakes) return;
        
        const offsetX = (Math.random() - 0.5) * shakeIntensity;
        const offsetY = (Math.random() - 0.5) * shakeIntensity;
        
        canvas.style.transform = `translate(${offsetX}px, ${offsetY}px)`;
        
        shakeCount++;
        shakeIntensity *= 0.9;
        
        setTimeout(() => {
            if (shakeCount < maxShakes) {
                requestAnimationFrame(shakeAnimation);
            } else {
                canvas.style.transform = 'translate(0, 0)';
            }
        }, 100);
    };
    
    shakeAnimation();
}

// 更新消息显示
function updateMessage(message) {
    gameState.message = message;
    const messageElement = document.getElementById('message');
    if (messageElement) {
        messageElement.textContent = message;
    }
}

// 找到从起点到终点的所有路径
function findAllPathsFromStartToEnd(startNode, endNode) {
    const paths = [];
    const visited = new Set();
    const currentPath = [];
    
    function dfs(node) {
        if (visited.has(node.id)) return;
        
        visited.add(node.id);
        currentPath.push(node);
        
        if (node.id === endNode.id) {
            // 找到目标，保存路径
            paths.push([...currentPath]);
        } else {
            // 继续搜索连接的节点
            const connectedNodes = getConnectedNodes(node);
            for (const connectedNode of connectedNodes) {
                if (!visited.has(connectedNode.id)) {
                    dfs(connectedNode);
                }
            }
        }
        
        currentPath.pop();
        visited.delete(node.id);
    }
    
    dfs(startNode);
    return paths;
}

// 获取节点的所有连接节点
function getConnectedNodes(node) {
    const connectedNodes = [];
    const outputPorts = node.outputPorts || [];
    
    for (const port of outputPorts) {
        if (port.connectedTo) {
            const connectedPort = findPortById(port.connectedTo);
            if (connectedPort) {
                const connectedNode = findNodeById(connectedPort.nodeId);
                if (connectedNode) {
                    connectedNodes.push(connectedNode);
                }
            }
        }
    }
    
    return connectedNodes;
}

// 找到两个节点之间的连接
function findConnectionBetweenNodes(fromNode, toNode) {
    return gameState.connections.find(conn => 
        conn.fromPort.nodeId === fromNode.id && 
        conn.toPort.nodeId === toNode.id
    );
}

// 通过ID找到节点
function findNodeById(nodeId) {
    return [...gameState.placedNodes, ...gameState.temporaryNodes].find(node => node.id === nodeId);
}

// 通过ID找到端口
function findPortById(portId) {
    for (const node of [...gameState.placedNodes, ...gameState.temporaryNodes]) {
        const allPorts = [...(node.inputPorts || []), ...(node.outputPorts || [])];
        const port = allPorts.find(p => p.id === portId);
        if (port) return port;
    }
    return null;
}

// 初始化游戏
function initGame() {
    updateMessage('蓝图连接游戏已启动！');
    
    // 初始化关卡配置
    gameState.levelConfig.currentLevelStartTime = Date.now();
    
    // 根据模式生成初始场景
    if (gameState.mode === 'puzzle') {
        generatePuzzleLevel();
    } else if (gameState.mode === 'tetris') {
        startTetrisLevel();
    } else {
        generateSolvableScenario();
    }
    
    // 设置事件监听器
    setupEventListeners();
    
    // 更新UI显示
    updateLevelDisplay();
    
    // 初始渲染
    render();
    
    // 更新交叉检测按钮状态
    setTimeout(updateCrossingButtonStates, 100); // 延迟确保DOM已加载
}

// 生成可解的游戏场景 - 重新设计算法
function generateSolvableScenario() {
    // 清空现有节点
    gameState.placedNodes = [];
    gameState.temporaryNodes = [];
    gameState.connections = [];
    
    // 使用改进的保证可解算法
    const level = gameState.level;
    const difficulty = gameState.difficultySettings[Math.min(level, 10)];
    const solvableNodeSet = generateGuaranteedSolvableScenario(difficulty);
    
    // 放置起点和终点
    solvableNodeSet.startNode.moveTo(gameState.placementArea.x + 50, gameState.placementArea.y + 100);
    solvableNodeSet.endNode.moveTo(gameState.placementArea.x + 600, gameState.placementArea.y + 100);
    
    gameState.placedNodes.push(solvableNodeSet.startNode, solvableNodeSet.endNode);
    
    // 将必需的中间节点放入临时区域
    solvableNodeSet.nodes.forEach((node, index) => {
        node.moveTo(gameState.temporaryArea.x + 10, gameState.temporaryArea.y + 10 + index * 100);
        gameState.temporaryNodes.push(node);
    });
    
    // 保存解决方案到游戏状态
    gameState.currentSolution = solvableNodeSet.guaranteedConnections;
    gameState.solutionVisible = false;
    
    console.log('✅ 生成保证可解场景:', {
        关卡: level,
        起点输出端口: solvableNodeSet.startNode.outputPorts.map(p => `${p.type}(${p.color})`),
        终点输入端口: solvableNodeSet.endNode.inputPorts.map(p => `${p.type}(${p.color})`),
        中间节点数量: solvableNodeSet.nodes.length,
        保证连接数量: solvableNodeSet.guaranteedConnections ? solvableNodeSet.guaranteedConnections.length : 0
    });
    
    updateMessage('新的可解场景已生成！按 H 键查看解决方案提示');
}

// 生成谜题模式关卡
function generatePuzzleLevel() {
    const level = gameState.level;
    const difficulty = gameState.difficultySettings[Math.min(level, 10)];
    
    // 在高关卡中有概率生成多节点场景
    if (level >= 5 && Math.random() < 0.3) {
        generateMultiNodeScenario();
        return;
    }
    
    // 清空现有节点
    gameState.placedNodes = [];
    gameState.temporaryNodes = [];
    gameState.connections = [];
    
    // 使用改进的保证可解算法
    const solvableNodeSet = generateGuaranteedSolvableScenario(difficulty);
    
    // 放置起点和终点
    solvableNodeSet.startNode.moveTo(gameState.placementArea.x + 50, gameState.placementArea.y + 100);
    solvableNodeSet.endNode.moveTo(gameState.placementArea.x + 600, gameState.placementArea.y + 100);
    
    gameState.placedNodes.push(solvableNodeSet.startNode, solvableNodeSet.endNode);
    
    // 将必需的中间节点放入临时区域
    solvableNodeSet.nodes.forEach((node, index) => {
        const x = gameState.temporaryArea.x + 10 + (index % 2) * 90;
        const y = gameState.temporaryArea.y + 10 + Math.floor(index / 2) * 90;
        node.moveTo(x, y);
        gameState.temporaryNodes.push(node);
    });
    
    // 保存解决方案到游戏状态
    gameState.currentSolution = solvableNodeSet.guaranteedConnections;
    gameState.solutionVisible = false;
    
    console.log(`🎯 生成关卡 ${level} 场景:`, {
        难度: difficulty,
        起点输出: solvableNodeSet.startNode.outputPorts.length,
        终点输入: solvableNodeSet.endNode.inputPorts.length,
        中间节点数量: solvableNodeSet.nodes.length,
        保证连接数量: solvableNodeSet.guaranteedConnections ? solvableNodeSet.guaranteedConnections.length : 0
    });
}

// 开始俄罗斯方块模式关卡
function startTetrisLevel() {
    const level = gameState.level;
    const difficulty = gameState.difficultySettings[Math.min(level, 10)];
    
    // 清空现有节点
    gameState.placedNodes = [];
    gameState.temporaryNodes = [];
    gameState.connections = [];
    
    // 生成保证可解的整体场景
    const solvableScenario = generateGuaranteedSolvableScenario(difficulty);
    
    // 放置起点和终点
    solvableScenario.startNode.moveTo(gameState.placementArea.x + 50, gameState.placementArea.y + 100);
    solvableScenario.endNode.moveTo(gameState.placementArea.x + 600, gameState.placementArea.y + 100);
    
    gameState.placedNodes.push(solvableScenario.startNode, solvableScenario.endNode);
    
    // 设置tetris模式状态
    gameState.tetrisMode.isActive = true;
    gameState.tetrisMode.lastDropTime = Date.now();
    gameState.tetrisMode.clearedLines = 0;
    gameState.tetrisMode.combo = 0;
    gameState.tetrisMode.overflowWarnings = 0;
    gameState.tetrisMode.timeRemaining = gameState.levelConfig.levelTimeLimit;
    
    // 设置关卡开始时间
    gameState.levelConfig.currentLevelStartTime = Date.now();
    
    // 生成基于可解场景的节点队列
    const nodePool = generateGuaranteedSolvableNodePool(solvableScenario, difficulty);
    const queueSize = calculateTetrisQueueSize(level);
    
    gameState.tetrisMode.nodeQueue = nodePool.slice(0, queueSize);
    gameState.tetrisMode.nextNodePreview = gameState.tetrisMode.nodeQueue[0];
    
    // 保存当前目标解决方案
    gameState.currentSolution = solvableScenario.guaranteedConnections;
    
    console.log(`🎮 启动俄罗斯方块模式关卡 ${level}:`, {
        难度: difficulty,
        起点输出: solvableScenario.startNode.outputPorts.length,
        终点输入: solvableScenario.endNode.inputPorts.length,
        节点队列长度: gameState.tetrisMode.nodeQueue.length,
        掉落间隔: gameState.tetrisMode.dropInterval + 'ms',
        保证连接数: solvableScenario.guaranteedConnections ? solvableScenario.guaranteedConnections.length : 0
    });
    
    // 开始节点掉落循环
    startTetrisDropLoop();
}

// 生成保证可解的节点池子
function generateGuaranteedSolvableNodePool(solvableScenario, difficulty) {
    const nodePool = [];
    
    // 1. 首先添加必需的节点（保证可解）
    const requiredNodes = [...solvableScenario.nodes];
    nodePool.push(...requiredNodes);
    
    // 2. 生成干扰节点（可选的额外节点）
    const distractorCount = Math.floor(difficulty.level * 1.5); // 关卡越高干扰越多
    const distractorNodes = generateSmartDistractorNodes(solvableScenario, distractorCount);
    nodePool.push(...distractorNodes);
    
    // 3. 打乱节点顺序
    return shuffleArray(nodePool);
}

// 生成智能干扰节点
function generateSmartDistractorNodes(solvableScenario, count) {
    const distractorNodes = [];
    
    for (let i = 0; i < count; i++) {
        const node = new Node('normal');
        node.id = `distractor_${i + 1}`;
        
        // 基于已存在的端口类型生成相似但不完全匹配的节点
        const availableInputs = [];
        const availableOutputs = [];
        
        // 收集所有可用的端口类型
        [solvableScenario.startNode, solvableScenario.endNode, ...solvableScenario.nodes].forEach(n => {
            if (n && n.inputPorts) {
                n.inputPorts.forEach(port => {
                    availableInputs.push({ type: port.type, color: port.color });
                });
            }
            if (n && n.outputPorts) {
                n.outputPorts.forEach(port => {
                    availableOutputs.push({ type: port.type, color: port.color });
                });
            }
        });
        
        // 随机选择输入和输出端口
        if (availableInputs.length > 0) {
            const inputChoice = availableInputs[Math.floor(Math.random() * availableInputs.length)];
            node.addInputPort(inputChoice.type, inputChoice.color);
        }
        
        if (availableOutputs.length > 0) {
            const outputChoice = availableOutputs[Math.floor(Math.random() * availableOutputs.length)];
            node.addOutputPort(outputChoice.type, outputChoice.color);
        }
        
        node.label = `Distractor ${i + 1}`;
        distractorNodes.push(node);
    }
    
    return distractorNodes;
}

// 计算俄罗斯方块队列大小（基于关卡）
function calculateTetrisQueueSize(level) {
    // 关卡越高，投放的节点越多
    const baseSize = 8;
    const additionalSize = Math.floor(level * 1.2);
    return Math.min(baseSize + additionalSize, 25); // 最多25个节点
}

// 打乱数组
function shuffleArray(array) {
    const shuffled = [...array];
    for (let i = shuffled.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
    }
    return shuffled;
}

// 改进后的算法 - 保证100%可解，基于层级DAG约束
function generateGuaranteedSolvableScenario(difficulty) {
    console.log(`🔧 生成100%可解的关卡 ${difficulty.level} 场景 (层级DAG算法)`);

    try {
        // 使用新的层级DAG确定性算法
        const scenario = generateDeterministicSolvableScenario(difficulty);

        // 验证生成的场景
        if (!scenario || !scenario.startNode || !scenario.endNode) {
            console.warn(`⚠️ 层级算法生成失败，使用后备算法`);
            return generateFallbackSolvableScenario(difficulty);
        }

        console.log(`✅ 层级DAG算法成功生成场景: ${scenario.nodes.length} 个中间节点`);
        return scenario;

    } catch (error) {
        console.error(`❌ 层级算法失败: ${error.message}`);
        return generateFallbackSolvableScenario(difficulty);
    }
}

// 后备算法 - 简化版本确保基本可解性
function generateFallbackSolvableScenario(difficulty) {
    console.log(`🔄 使用后备算法生成关卡 ${difficulty.level}`);

    // 创建最简单的可解场景
    const startNode = new Node('start');
    startNode.label = 'Start';
    startNode.id = 'start_node';
    startNode.depth = 0;

    const endNode = new Node('end');
    endNode.label = 'End';
    endNode.id = 'end_node';
    endNode.depth = 1;

    // 添加简单的端口对
    const portType = difficulty.availableTypes[0];
    const portColor = difficulty.availableColors[0];

    startNode.addOutputPort(portType, portColor);
    endNode.addInputPort(portType, portColor);

    return {
        startNode: startNode,
        endNode: endNode,
        nodes: [],
        level: difficulty.level,
        guaranteedConnections: [{
            from: { node: startNode.id, type: portType, color: portColor },
            to: { node: endNode.id, type: portType, color: portColor },
            sourceDepth: 0,
            targetDepth: 1
        }],
        nodesByLayer: new Map([[0, [startNode]], [1, [endNode]]]),
        allNodes: [startNode, endNode]
    };
}

// 100%确定性可解场景生成算法 - 基于层级DAG约束
function generateDeterministicSolvableScenario(difficulty) {
    console.log(`🎯 使用层级DAG算法生成关卡 ${difficulty.level}`);

    // 第1步：计算层级化场景规格
    const scenarioSpec = calculateLayeredScenarioSpec(difficulty);
    console.log(`📊 层级场景规格: ${JSON.stringify(scenarioSpec)}`);

    // 第2步：生成层级化端口规划
    const layeredPortPlan = generateLayeredPortPlan(scenarioSpec);
    console.log(`🔗 层级端口规划: ${layeredPortPlan.totalPairs}对端口, ${layeredPortPlan.maxDepth + 1}层`);

    // 第3步：基于层级规划创建DAG节点
    const scenario = buildLayeredScenarioFromPortPlan(layeredPortPlan, difficulty);

    // 第4步：验证DAG约束和可解性
    const validation = validateLayeredScenarioCompletely(scenario);
    if (!validation.isValid) {
        console.error(`❌ 层级验证失败: ${validation.errors.join(', ')}`);
        return generateMinimalSolvableScenario(difficulty);
    }

    console.log(`✅ 层级DAG算法成功生成100%可解场景`);
    return scenario;
}

// 计算层级化场景规格 - 基于DAG拓扑约束
function calculateLayeredScenarioSpec(difficulty) {
    const spec = {
        level: difficulty.level,
        // 计算层级深度 (起点=0, 终点=maxDepth)
        maxDepth: calculateMaxDepth(difficulty.level),
        // 每个关卡的端口对数量
        portPairs: calculatePortPairs(difficulty.level),
        // 基于关卡动态选择类型和颜色
        useTypes: selectTypesForLevel(difficulty),
        useColors: selectColorsForLevel(difficulty),
        // 每层的中间节点数量分布
        layerNodeDistribution: calculateLayerNodeDistribution(difficulty.level),
        // 层间连接约束
        layerConnectivityRules: calculateLayerConnectivityRules(difficulty.level)
    };

    return spec;
}

// 计算最大深度 - 确保合理的层级分布
function calculateMaxDepth(level) {
    switch(level) {
        case 1: return 1;  // 起点→终点 (2层)
        case 2: return 1;  // 起点→终点 (2层)
        case 3: return 2;  // 起点→中间→终点 (3层)
        case 4: return 2;  // 起点→中间→终点 (3层)
        case 5: return 3;  // 起点→中间→中间→终点 (4层)
        default: return Math.min(level - 2, 4); // 最多5层
    }
}

// 计算每层节点分布
function calculateLayerNodeDistribution(level) {
    const maxDepth = calculateMaxDepth(level);
    const distribution = new Array(maxDepth + 1).fill(0);

    // 起点层 (depth 0) 和终点层 (depth maxDepth) 各1个节点
    distribution[0] = 1; // 起点
    distribution[maxDepth] = 1; // 终点

    // 中间层节点分布
    for (let depth = 1; depth < maxDepth; depth++) {
        distribution[depth] = Math.max(1, Math.floor(level / 2)); // 至少1个中间节点
    }

    return distribution;
}

// 计算层间连接规则
function calculateLayerConnectivityRules(level) {
    return {
        // 每个节点必须至少有一个前驱和后继 (除起点和终点)
        minPredecessors: 1,
        minSuccessors: 1,
        // 允许跨层连接 (depth差值最大为1，确保DAG性质)
        maxDepthGap: 1,
        // 端口流守恒
        enforcePortFlowConservation: true
    };
}

// 根据关卡计算端口对数量
function calculatePortPairs(level) {
    switch(level) {
        case 1: return 1;  // 关卡1：最简单，1对端口
        case 2: return 1;  // 关卡2：1对端口，但类型/颜色不同
        case 3: return 2;  // 关卡3：开始增加复杂度
        case 4: return 2;  // 关卡4：2对端口
        case 5: return 3;  // 关卡5：3对端口
        default: return Math.min(level - 2, 4); // 关卡6+：最多4对端口
    }
}

// 根据关卡计算转换节点数量
function calculateTransformNodes(level) {
    switch(level) {
        case 1: return 0;  // 关卡1：无转换
        case 2: return 0;  // 关卡2：无转换
        case 3: return 1;  // 关卡3：1个转换
        case 4: return 1;  // 关卡4：1个转换
        case 5: return 2;  // 关卡5：2个转换
        default: return Math.min(level - 3, 3); // 关卡6+：最多3个转换
    }
}

// 为关卡选择类型
function selectTypesForLevel(difficulty) {
    const maxTypes = Math.min(difficulty.level + 1, difficulty.availableTypes.length);
    return difficulty.availableTypes.slice(0, maxTypes);
}

// 为关卡选择颜色
function selectColorsForLevel(difficulty) {
    const maxColors = Math.min(difficulty.level + 1, difficulty.availableColors.length);
    return difficulty.availableColors.slice(0, maxColors);
}

// 根据关卡选择类型
function selectTypesForLevel(difficulty) {
    const level = difficulty.level;
    const maxTypes = Math.min(difficulty.types, difficulty.availableTypes.length);
    
    // 确保每个关卡有不同的类型组合
    switch(level) {
        case 1: return ['square']; // 关卡1：只用方形
        case 2: return ['circle']; // 关卡2：只用圆形
        case 3: return ['square', 'circle']; // 关卡3：方形+圆形
        case 4: return ['triangle']; // 关卡4：只用三角形（单类型增加挑战）
        case 5: return ['square', 'triangle']; // 关卡5：方形+三角形
        default: return difficulty.availableTypes.slice(0, maxTypes);
    }
}

// 根据关卡选择颜色
function selectColorsForLevel(difficulty) {
    const level = difficulty.level;
    const maxColors = Math.min(difficulty.colors, difficulty.availableColors.length);
    
    // 确保每个关卡有不同的颜色组合
    switch(level) {
        case 1: return ['#ff5252']; // 关卡1：只用红色
        case 2: return ['#2196F3']; // 关卡2：只用蓝色
        case 3: return ['#4CAF50']; // 关卡3：只用绿色
        case 4: return ['#ff5252', '#2196F3']; // 关卡4：红色+蓝色
        case 5: return ['#2196F3', '#4CAF50']; // 关卡5：蓝色+绿色
        default: return difficulty.availableColors.slice(0, maxColors);
    }
}

// 根据关卡计算转换节点数量
function calculateTransformNodes(level) {
    switch(level) {
        case 1: return 0; // 关卡1：无转换，直接连接
        case 2: return 0; // 关卡2：无转换，直接连接
        case 3: return 1; // 关卡3：开始有转换节点
        case 4: return 1; // 关卡4：1个转换节点
        case 5: return 2; // 关卡5：2个转换节点
        default: return Math.min(level - 3, 3); // 关卡6+：最多3个转换节点
    }
}

// 生成层级化端口规划 - 基于DAG拓扑约束
function generateLayeredPortPlan(scenarioSpec) {
    const plan = {
        totalPairs: scenarioSpec.portPairs,
        maxDepth: scenarioSpec.maxDepth,
        layerNodeDistribution: scenarioSpec.layerNodeDistribution,
        portsByLayer: new Map(), // 每层的端口分配
        portTypeBalance: new Map(), // 端口类型平衡表
        connectionPaths: [], // 预定义的连接路径
        inputCount: 0,
        outputCount: 0
    };

    // 初始化每层端口分配
    for (let depth = 0; depth <= scenarioSpec.maxDepth; depth++) {
        plan.portsByLayer.set(depth, {
            inputPorts: [],
            outputPorts: [],
            nodeCount: scenarioSpec.layerNodeDistribution[depth]
        });
    }

    // 生成端口类型分布 - 确保严格平衡
    const portTypeDistribution = generateBalancedPortTypeDistribution(scenarioSpec);

    // 为每个端口对创建层级化路径
    for (let i = 0; i < scenarioSpec.portPairs; i++) {
        const portType = portTypeDistribution[i];
        const connectionPath = createLayeredConnectionPath(portType, scenarioSpec, i);

        plan.connectionPaths.push(connectionPath);

        // 分配端口到相应层级
        assignPortsToLayers(connectionPath, plan);

        // 更新端口类型平衡
        updatePortTypeBalance(portType, plan.portTypeBalance);
    }

    // 验证端口平衡
    const balanceValidation = validatePortTypeBalance(plan.portTypeBalance);
    if (!balanceValidation.isValid) {
        console.error(`❌ 端口类型不平衡: ${balanceValidation.errors.join(', ')}`);
    }

    console.log(`🎯 层级端口平衡验证: 输入${plan.inputCount}个，输出${plan.outputCount}个`);
    return plan;
}

// 生成平衡的端口类型分布
function generateBalancedPortTypeDistribution(scenarioSpec) {
    const distribution = [];

    for (let i = 0; i < scenarioSpec.portPairs; i++) {
        const typeIndex = i % scenarioSpec.useTypes.length;
        const colorIndex = i % scenarioSpec.useColors.length;

        distribution.push({
            type: scenarioSpec.useTypes[typeIndex],
            color: scenarioSpec.useColors[colorIndex],
            id: `port_type_${i + 1}`
        });
    }

    return distribution;
}

// 创建层级化连接路径 - 修正版本，确保端口平衡
function createLayeredConnectionPath(portType, scenarioSpec, pathIndex) {
    const path = {
        id: `path_${pathIndex + 1}`,
        portType: portType,
        layers: [],
        transformations: []
    };

    // 根据最大深度创建路径，确保起点和终点使用相同的端口类型
    for (let depth = 0; depth <= scenarioSpec.maxDepth; depth++) {
        if (depth === 0) {
            // 起点层 - 只有输出端口
            path.layers.push({
                depth: depth,
                nodeType: 'start',
                outputPort: { ...portType, side: 'output' }
            });
        } else if (depth === scenarioSpec.maxDepth) {
            // 终点层 - 只有输入端口，使用相同的端口类型确保平衡
            path.layers.push({
                depth: depth,
                nodeType: 'end',
                inputPort: { ...portType, side: 'input' }
            });
        } else {
            // 中间层 - 使用直通连接（相同类型输入输出）确保平衡
            path.layers.push({
                depth: depth,
                nodeType: 'passthrough',
                inputPort: { ...portType, side: 'input' },
                outputPort: { ...portType, side: 'output' }
            });
        }
    }

    return path;
}

// 判断是否需要在指定深度添加转换
function shouldAddTransformation(depth, scenarioSpec, pathIndex) {
    // 简单策略：在中间层添加转换，但不是所有路径都需要
    if (scenarioSpec.level <= 2) return false; // 低级别不需要转换

    // 根据路径索引和深度决定是否需要转换
    return (pathIndex + depth) % 2 === 0 && depth === Math.floor(scenarioSpec.maxDepth / 2);
}

// 创建端口转换
function createPortTransformation(originalPortType, scenarioSpec) {
    const inputType = originalPortType.type;
    const inputColor = originalPortType.color;

    // 简单转换：改变类型或颜色
    const outputType = getNextType(inputType, scenarioSpec.useTypes);
    const outputColor = getNextColor(inputColor, scenarioSpec.useColors);

    return {
        input: { type: inputType, color: inputColor, side: 'input' },
        output: { type: outputType, color: outputColor, side: 'output' },
        transformationType: inputType !== outputType ? 'type' : 'color'
    };
}

// 获取下一个类型（循环）
function getNextType(currentType, availableTypes) {
    const currentIndex = availableTypes.indexOf(currentType);
    const nextIndex = (currentIndex + 1) % availableTypes.length;
    return availableTypes[nextIndex];
}

// 获取下一个颜色（循环）
function getNextColor(currentColor, availableColors) {
    const currentIndex = availableColors.indexOf(currentColor);
    const nextIndex = (currentIndex + 1) % availableColors.length;
    return availableColors[nextIndex];
}

// 分配端口到层级
function assignPortsToLayers(connectionPath, plan) {
    connectionPath.layers.forEach(layer => {
        const layerData = plan.portsByLayer.get(layer.depth);

        if (layer.inputPort) {
            layerData.inputPorts.push(layer.inputPort);
            plan.inputCount++;
        }

        if (layer.outputPort) {
            layerData.outputPorts.push(layer.outputPort);
            plan.outputCount++;
        }
    });
}

// 更新端口类型平衡 - 修正版本，不在这里计算平衡
function updatePortTypeBalance(portType, balanceMap) {
    const key = `${portType.type}:${portType.color}`;
    if (!balanceMap.has(key)) {
        balanceMap.set(key, { input: 0, output: 0 });
    }

    // 注意：实际的平衡计算在 validateScenarioPortTypeBalance 中进行
    // 这里只是初始化类型记录
}

// 验证端口类型平衡
function validatePortTypeBalance(balanceMap) {
    const errors = [];

    for (const [typeKey, balance] of balanceMap) {
        if (balance.input !== balance.output) {
            errors.push(`类型${typeKey}不平衡: 输入${balance.input}, 输出${balance.output}`);
        }
    }

    return {
        isValid: errors.length === 0,
        errors: errors
    };
}

// 基于层级端口规划构建场景
function buildLayeredScenarioFromPortPlan(layeredPortPlan, difficulty) {
    // 创建起点节点 (深度 0)
    const startNode = new Node('start');
    startNode.label = 'Start';
    startNode.id = 'start_node';
    startNode.depth = 0;

    // 创建终点节点 (最大深度)
    const endNode = new Node('end');
    endNode.label = 'End';
    endNode.id = 'end_node';
    endNode.depth = layeredPortPlan.maxDepth;

    // 按层级创建中间节点
    const nodesByLayer = new Map();
    const allNodes = [];

    // 初始化每层节点容器
    for (let depth = 0; depth <= layeredPortPlan.maxDepth; depth++) {
        nodesByLayer.set(depth, []);
    }

    // 添加起点和终点到相应层级
    nodesByLayer.get(0).push(startNode);
    nodesByLayer.get(layeredPortPlan.maxDepth).push(endNode);
    allNodes.push(startNode, endNode);
    
    // 创建中间层节点并分配端口 - 确保每个路径有专用节点
    for (let depth = 1; depth < layeredPortPlan.maxDepth; depth++) {
        const layerData = layeredPortPlan.portsByLayer.get(depth);

        // 计算这一层需要的路径数量
        const pathsNeedingThisDepth = layeredPortPlan.connectionPaths.filter(path =>
            path.layers.some(layer => layer.depth === depth)
        );

        // 为每个路径创建一个专用的中间节点，确保端口平衡
        const nodeCount = Math.max(pathsNeedingThisDepth.length, layerData.nodeCount);

        for (let nodeIndex = 0; nodeIndex < nodeCount; nodeIndex++) {
            const intermediateNode = new Node('normal');
            intermediateNode.id = `layer_${depth}_node_${nodeIndex + 1}`;
            intermediateNode.depth = depth;
            intermediateNode.label = `L${depth}N${nodeIndex + 1}`;

            nodesByLayer.get(depth).push(intermediateNode);
            allNodes.push(intermediateNode);
        }
    }

    // 根据连接路径分配端口
    layeredPortPlan.connectionPaths.forEach((path, pathIndex) => {
        path.layers.forEach(layer => {
            const layerNodes = nodesByLayer.get(layer.depth);

            if (layer.depth === 0) {
                // 起点层 - 添加输出端口
                if (layer.outputPort) {
                    startNode.addOutputPort(layer.outputPort.type, layer.outputPort.color);
                }
            } else if (layer.depth === layeredPortPlan.maxDepth) {
                // 终点层 - 添加输入端口
                if (layer.inputPort) {
                    endNode.addInputPort(layer.inputPort.type, layer.inputPort.color);
                }
            } else {
                // 中间层 - 为每个路径分配专用节点确保平衡
                const targetNode = layerNodes[pathIndex % layerNodes.length];

                if (layer.inputPort) {
                    targetNode.addInputPort(layer.inputPort.type, layer.inputPort.color);
                }
                if (layer.outputPort) {
                    targetNode.addOutputPort(layer.outputPort.type, layer.outputPort.color);
                }

                // 更新节点标签
                if (layer.transformation) {
                    const trans = layer.transformation;
                    targetNode.label = `${trans.input.type}:${trans.input.color.substr(1,2)}→${trans.output.type}:${trans.output.color.substr(1,2)}`;
                } else {
                    // 直通节点
                    targetNode.label = `${layer.inputPort.type}:${layer.inputPort.color.substr(1,2)}`;
                }
            }
        });
    });
    
    // 生成层级化连接方案 - 确保DAG约束
    const guaranteedConnections = [];

    // 为每个连接路径创建连接
    layeredPortPlan.connectionPaths.forEach((path, pathIndex) => {
        // 获取路径中的所有层级
        const pathLayers = path.layers;

        // 创建层级间的连接
        for (let i = 0; i < pathLayers.length - 1; i++) {
            const sourceLayer = pathLayers[i];
            const targetLayer = pathLayers[i + 1];

            // 获取源节点和目标节点
            const sourceNodes = nodesByLayer.get(sourceLayer.depth);
            const targetNodes = nodesByLayer.get(targetLayer.depth);

            const sourceNode = sourceLayer.depth === 0 ?
                startNode : sourceNodes[pathIndex % sourceNodes.length];

            const targetNode = targetLayer.depth === layeredPortPlan.maxDepth ?
                endNode : targetNodes[pathIndex % targetNodes.length];

            // 创建连接
            if (sourceLayer.outputPort && targetLayer.inputPort) {
                guaranteedConnections.push({
                    from: {
                        node: sourceNode.id,
                        type: sourceLayer.outputPort.type,
                        color: sourceLayer.outputPort.color
                    },
                    to: {
                        node: targetNode.id,
                        type: targetLayer.inputPort.type,
                        color: targetLayer.inputPort.color
                    },
                    sourceDepth: sourceLayer.depth,
                    targetDepth: targetLayer.depth
                });
            }
        }
    });

    // 确保所有中间节点都有连接
    ensureAllNodesHaveConnections(allNodes, guaranteedConnections, layeredPortPlan);

    // 收集所有中间节点
    const intermediateNodes = allNodes.filter(node =>
        node.id !== startNode.id && node.id !== endNode.id
    );

    return {
        startNode: startNode,
        endNode: endNode,
        nodes: intermediateNodes,
        level: difficulty.level,
        portPlan: layeredPortPlan,
        guaranteedConnections: guaranteedConnections,
        nodesByLayer: nodesByLayer,
        allNodes: allNodes
    };
}

// 确保所有节点都有连接 - 满足连通性约束
function ensureAllNodesHaveConnections(allNodes, guaranteedConnections, layeredPortPlan) {
    // 检查每个中间节点是否都在至少一条路径上
    const connectedNodes = new Set();

    guaranteedConnections.forEach(conn => {
        connectedNodes.add(conn.from.node);
        connectedNodes.add(conn.to.node);
    });

    // 为未连接的中间节点创建连接
    allNodes.forEach(node => {
        if (node.type === 'normal' && !connectedNodes.has(node.id)) {
            // 创建到前一层和后一层的连接
            createConnectionsForIsolatedNode(node, allNodes, guaranteedConnections, layeredPortPlan);
        }
    });
}

// 为孤立节点创建连接
function createConnectionsForIsolatedNode(isolatedNode, allNodes, guaranteedConnections, layeredPortPlan) {
    const nodeDepth = isolatedNode.depth;

    // 寻找前一层的节点作为输入源
    if (nodeDepth > 0) {
        const predecessorNodes = allNodes.filter(n => n.depth === nodeDepth - 1);
        if (predecessorNodes.length > 0 && isolatedNode.inputPorts.length > 0) {
            const sourceNode = predecessorNodes[0];
            const inputPort = isolatedNode.inputPorts[0];

            // 为源节点添加匹配的输出端口（如果没有）
            if (!sourceNode.outputPorts.some(p => p.type === inputPort.type && p.color === inputPort.color)) {
                sourceNode.addOutputPort(inputPort.type, inputPort.color);
            }

            guaranteedConnections.push({
                from: { node: sourceNode.id, type: inputPort.type, color: inputPort.color },
                to: { node: isolatedNode.id, type: inputPort.type, color: inputPort.color },
                sourceDepth: nodeDepth - 1,
                targetDepth: nodeDepth
            });
        }
    }

    // 寻找后一层的节点作为输出目标
    if (nodeDepth < layeredPortPlan.maxDepth) {
        const successorNodes = allNodes.filter(n => n.depth === nodeDepth + 1);
        if (successorNodes.length > 0 && isolatedNode.outputPorts.length > 0) {
            const targetNode = successorNodes[0];
            const outputPort = isolatedNode.outputPorts[0];

            // 为目标节点添加匹配的输入端口（如果没有）
            if (!targetNode.inputPorts.some(p => p.type === outputPort.type && p.color === outputPort.color)) {
                targetNode.addInputPort(outputPort.type, outputPort.color);
            }

            guaranteedConnections.push({
                from: { node: isolatedNode.id, type: outputPort.type, color: outputPort.color },
                to: { node: targetNode.id, type: outputPort.type, color: outputPort.color },
                sourceDepth: nodeDepth,
                targetDepth: nodeDepth + 1
            });
        }
    }
}

// 确定性地获取源类型（用于转换）
function getSourceType(targetType, availableTypes) {
    // 简单的确定性选择：选择下一个可用类型
    const currentIndex = availableTypes.indexOf(targetType);
    const nextIndex = (currentIndex + 1) % availableTypes.length;
    return availableTypes[nextIndex];
}

// 确定性地获取源颜色（用于转换）
function getSourceColor(targetColor, availableColors) {
    // 简单的确定性选择：选择下一个可用颜色
    const currentIndex = availableColors.indexOf(targetColor);
    const nextIndex = (currentIndex + 1) % availableColors.length;
    return availableColors[nextIndex];
}

// 全面验证层级化场景的DAG约束和可解性
function validateLayeredScenarioCompletely(scenario) {
    const errors = [];
    const warnings = [];

    // 验证1：基本结构
    if (!scenario.startNode) errors.push('缺少起点节点');
    if (!scenario.endNode) errors.push('缺少终点节点');
    if (!scenario.startNode?.outputPorts?.length) errors.push('起点没有输出端口');
    if (!scenario.endNode?.inputPorts?.length) errors.push('终点没有输入端口');

    // 验证2：层级结构约束
    const layerValidation = validateLayerStructure(scenario);
    errors.push(...layerValidation.errors);
    warnings.push(...layerValidation.warnings);

    // 验证3：DAG拓扑约束
    const dagValidation = validateDAGTopology(scenario);
    errors.push(...dagValidation.errors);
    warnings.push(...dagValidation.warnings);

    // 验证4：端口流守恒
    const flowValidation = validatePortFlowConservation(scenario);
    errors.push(...flowValidation.errors);
    warnings.push(...flowValidation.warnings);

    // 验证5：连通性约束
    const connectivityValidation = validateLayeredConnectivity(scenario);
    errors.push(...connectivityValidation.errors);
    warnings.push(...connectivityValidation.warnings);

    // 验证6：端口类型平衡
    const typeBalanceValidation = validateScenarioPortTypeBalance(scenario);
    errors.push(...typeBalanceValidation.errors);
    warnings.push(...typeBalanceValidation.warnings);

    return {
        isValid: errors.length === 0,
        errors: errors,
        warnings: warnings,
        details: {
            layerCount: scenario.nodesByLayer ? scenario.nodesByLayer.size : 0,
            totalNodes: scenario.allNodes ? scenario.allNodes.length : 0,
            connectionCount: scenario.guaranteedConnections ? scenario.guaranteedConnections.length : 0
        }
    };
}

// 验证层级结构约束
function validateLayerStructure(scenario) {
    const errors = [];
    const warnings = [];

    if (!scenario.nodesByLayer) {
        errors.push('缺少层级结构信息');
        return { errors, warnings };
    }

    // 检查起点在深度0
    if (scenario.startNode.depth !== 0) {
        errors.push(`起点深度错误: 期望0, 实际${scenario.startNode.depth}`);
    }

    // 检查终点在最大深度
    const maxDepth = Math.max(...Array.from(scenario.nodesByLayer.keys()));
    if (scenario.endNode.depth !== maxDepth) {
        errors.push(`终点深度错误: 期望${maxDepth}, 实际${scenario.endNode.depth}`);
    }

    // 检查每层至少有一个节点
    for (let depth = 0; depth <= maxDepth; depth++) {
        const layerNodes = scenario.nodesByLayer.get(depth);
        if (!layerNodes || layerNodes.length === 0) {
            errors.push(`层级${depth}没有节点`);
        }
    }

    // 检查中间节点深度分配
    scenario.allNodes.forEach(node => {
        if (node.type === 'normal') {
            if (node.depth <= 0 || node.depth >= maxDepth) {
                errors.push(`中间节点${node.id}深度错误: ${node.depth}`);
            }
        }
    });

    return { errors, warnings };
}

// 验证DAG拓扑约束
function validateDAGTopology(scenario) {
    const errors = [];
    const warnings = [];

    if (!scenario.guaranteedConnections) {
        errors.push('缺少连接信息');
        return { errors, warnings };
    }

    // 检查所有连接都满足深度约束 (depth(u) < depth(v))
    scenario.guaranteedConnections.forEach((conn, index) => {
        if (conn.sourceDepth >= conn.targetDepth) {
            errors.push(`连接${index + 1}违反深度约束: ${conn.sourceDepth} >= ${conn.targetDepth}`);
        }

        // 检查深度差不超过1（相邻层连接）
        if (conn.targetDepth - conn.sourceDepth > 1) {
            warnings.push(`连接${index + 1}跨越多层: ${conn.sourceDepth} -> ${conn.targetDepth}`);
        }
    });

    return { errors, warnings };
}

// 验证端口流守恒
function validatePortFlowConservation(scenario) {
    const errors = [];
    const warnings = [];

    scenario.allNodes.forEach(node => {
        if (node.type === 'start') {
            // 起点：只有输出端口
            if (node.inputPorts.length > 0) {
                errors.push(`起点${node.id}不应有输入端口`);
            }
            if (node.outputPorts.length === 0) {
                errors.push(`起点${node.id}必须有输出端口`);
            }
        } else if (node.type === 'end') {
            // 终点：只有输入端口
            if (node.outputPorts.length > 0) {
                errors.push(`终点${node.id}不应有输出端口`);
            }
            if (node.inputPorts.length === 0) {
                errors.push(`终点${node.id}必须有输入端口`);
            }
        } else if (node.type === 'normal') {
            // 中间节点：必须有输入和输出端口
            if (node.inputPorts.length === 0) {
                errors.push(`中间节点${node.id}必须有输入端口`);
            }
            if (node.outputPorts.length === 0) {
                errors.push(`中间节点${node.id}必须有输出端口`);
            }
        }
    });

    return { errors, warnings };
}

// 验证层级连通性
function validateLayeredConnectivity(scenario) {
    const errors = [];
    const warnings = [];

    // 检查每个中间节点都在至少一条有效路径上
    const connectedNodes = new Set();

    scenario.guaranteedConnections.forEach(conn => {
        connectedNodes.add(conn.from.node);
        connectedNodes.add(conn.to.node);
    });

    scenario.allNodes.forEach(node => {
        if (node.type === 'normal' && !connectedNodes.has(node.id)) {
            errors.push(`中间节点${node.id}未连接到任何路径`);
        }
    });

    // 检查是否存在从起点到终点的完整路径
    const pathExists = checkPathExistence(scenario.startNode, scenario.endNode, scenario.guaranteedConnections);
    if (!pathExists) {
        errors.push('不存在从起点到终点的完整路径');
    }

    return { errors, warnings };
}

// 验证场景端口类型平衡
function validateScenarioPortTypeBalance(scenario) {
    const errors = [];
    const warnings = [];
    const typeBalance = new Map();

    // 统计所有端口类型
    scenario.allNodes.forEach(node => {
        node.inputPorts.forEach(port => {
            const key = `${port.type}:${port.color}`;
            if (!typeBalance.has(key)) {
                typeBalance.set(key, { input: 0, output: 0 });
            }
            typeBalance.get(key).input++;
        });

        node.outputPorts.forEach(port => {
            const key = `${port.type}:${port.color}`;
            if (!typeBalance.has(key)) {
                typeBalance.set(key, { input: 0, output: 0 });
            }
            typeBalance.get(key).output++;
        });
    });

    // 检查平衡
    for (const [typeKey, balance] of typeBalance) {
        if (balance.input !== balance.output) {
            errors.push(`端口类型${typeKey}不平衡: 输入${balance.input}, 输出${balance.output}`);
        }
    }

    return { errors, warnings };
}

// 检查路径存在性
function checkPathExistence(startNode, endNode, connections) {
    const graph = new Map();

    // 构建邻接表
    connections.forEach(conn => {
        if (!graph.has(conn.from.node)) {
            graph.set(conn.from.node, []);
        }
        graph.get(conn.from.node).push(conn.to.node);
    });

    // BFS查找路径
    const queue = [startNode.id];
    const visited = new Set();

    while (queue.length > 0) {
        const currentNodeId = queue.shift();

        if (visited.has(currentNodeId)) continue;
        visited.add(currentNodeId);

        if (currentNodeId === endNode.id) return true;

        const neighbors = graph.get(currentNodeId) || [];
        neighbors.forEach(neighbor => {
            if (!visited.has(neighbor)) {
                queue.push(neighbor);
            }
        });
    }

    return false;
}

// 验证端口类型匹配
function validatePortTypeMatching(scenario) {
    const errors = [];
    const warnings = [];
    const allNodes = [scenario.startNode, scenario.endNode, ...scenario.nodes];
    
    // 统计输入和输出端口的类型分布
    const inputTypes = new Map();
    const outputTypes = new Map();
    
    allNodes.forEach(node => {
        if (node?.inputPorts) {
            node.inputPorts.forEach(port => {
                const key = `${port.type}:${port.color}`;
                inputTypes.set(key, (inputTypes.get(key) || 0) + 1);
            });
        }
        
        if (node?.outputPorts) {
            node.outputPorts.forEach(port => {
                const key = `${port.type}:${port.color}`;
                outputTypes.set(key, (outputTypes.get(key) || 0) + 1);
            });
        }
    });
    
    // 检查每种端口类型的数量是否匹配
    const allPortTypes = new Set([...inputTypes.keys(), ...outputTypes.keys()]);
    
    for (const portType of allPortTypes) {
        const inputCount = inputTypes.get(portType) || 0;
        const outputCount = outputTypes.get(portType) || 0;
        
        if (inputCount !== outputCount) {
            errors.push(`端口类型${portType}不匹配: 输入${inputCount}个, 输出${outputCount}个`);
        }
    }
    
    return { errors, warnings };
}

// 验证完整路径连通性
function validateCompletePath(scenario) {
    try {
        const allNodes = [scenario.startNode, scenario.endNode, ...scenario.nodes];
        
        // 验证每个终点输入端口都能从起点到达
        for (const targetPort of scenario.endNode.inputPorts) {
            let canReach = false;
            
            // 尝试从起点的每个输出端口找路径
            for (const sourcePort of scenario.startNode.outputPorts) {
                if (findPathBetweenPorts(sourcePort, targetPort, allNodes)) {
                    canReach = true;
                    break;
                }
            }
            
            if (!canReach) {
                return {
                    isConnected: false,
                    reason: `无法从起点到达终点端口${targetPort.type}:${targetPort.color}`
                };
            }
        }
        
        return { isConnected: true };
    } catch (error) {
        return {
            isConnected: false,
            reason: `路径验证出错: ${error.message}`
        };
    }
}

// 在端口之间查找路径
function findPathBetweenPorts(sourcePort, targetPort, allNodes) {
    // 如果直接匹配，返回true
    if (sourcePort.type === targetPort.type && sourcePort.color === targetPort.color) {
        return true;
    }
    
    // 查找中间转换节点
    for (const node of allNodes) {
        if (node.type === 'normal') {
            // 检查是否存在能够进行转换的节点
            const hasMatchingInput = node.inputPorts.some(port => 
                port.type === sourcePort.type && port.color === sourcePort.color
            );
            const hasMatchingOutput = node.outputPorts.some(port => 
                port.type === targetPort.type && port.color === targetPort.color
            );
            
            if (hasMatchingInput && hasMatchingOutput) {
                return true;
            }
        }
    }
    
    return false;
}

// 生成最小可解场景（备用方案）
function generateMinimalSolvableScenario(difficulty) {
    console.log(`🔧 生成最小可解场景 - 关卡 ${difficulty.level}`);
    
    const startNode = new Node('start');
    const endNode = new Node('end');
    
    // 使用最简单的配置
    const type = difficulty.availableTypes[0];
    const color = difficulty.availableColors[0];
    
    startNode.addOutputPort(type, color);
    endNode.addInputPort(type, color);
    
    startNode.label = 'Start';
    endNode.label = 'End';
    startNode.id = 'start_node';
    endNode.id = 'end_node';
    
    return {
        startNode: startNode,
        endNode: endNode,
        nodes: [],
        level: difficulty.level,
        isMinimal: true,
        guaranteedConnections: [] // 添加缺失的属性以保持兼容性
    };
}

// 生成终点需求 - 确保合理分布
function generateImprovedEndRequirements(difficulty) {
    const requirements = [];
    // 修正：确保需求数量合理，至少有1个，最多不超过难度配置
    const requiredCount = Math.max(1, Math.min(difficulty.chains, Math.floor(difficulty.level / 2) + 1));
    
    // 平衡分布类型和颜色
    const availableTypes = [...difficulty.availableTypes];
    const availableColors = [...difficulty.availableColors];
    
    for (let i = 0; i < requiredCount; i++) {
        const type = availableTypes[i % availableTypes.length];
        const color = availableColors[i % availableColors.length];
        
        requirements.push({
            type: type,
            color: color,
            id: `end_req_${i + 1}`
        });
    }
    
    return requirements;
}

// 生成匹配的起点 - 保证至少有一条路径可达
function generateImprovedStartProvisions(endRequirements, difficulty) {
    const provisions = [];
    
    // 为每个终点需求创建一个可转换的起点
    endRequirements.forEach((req, index) => {
        let sourceType, sourceColor;
        
        if (difficulty.level <= 2) {
            // 低难度：直接匹配
            sourceType = req.type;
            sourceColor = req.color;
        } else {
            // 高难度：需要转换
            sourceType = getAlternativeType(req.type, difficulty.availableTypes);
            sourceColor = getAlternativeColor(req.color, difficulty.availableColors);
        }
        
        provisions.push({
            type: sourceType,
            color: sourceColor,
            id: `start_prov_${index + 1}`,
            targetRequirement: req.id
        });
    });
    
    return provisions;
}

// 生成连接的转换路径
function generateConnectedTransformationPath(startProvisions, endRequirements, difficulty) {
    const pathNodes = [];
    const connections = [];
    
    // 为每个起点-终点对创建转换路径
    startProvisions.forEach((provision, index) => {
        const target = endRequirements[index];
        const path = createTransformationPath(provision, target, difficulty, index);
        
        pathNodes.push(...path.nodes);
        connections.push(...path.connections);
    });
    
    // 添加额外的节点以增加复杂性和多解可能性
    if (difficulty.level > 3) {
        const extraNodes = generateExtraNodes(pathNodes, difficulty);
        pathNodes.push(...extraNodes);
    }
    
    return {
        nodes: pathNodes,
        connections: connections
    };
}

// 创建单条转换路径
function createTransformationPath(source, target, difficulty, pathIndex) {
    const pathNodes = [];
    const connections = [];
    
    let currentType = source.type;
    let currentColor = source.color;
    const targetType = target.type;
    const targetColor = target.color;
    
    let nodeIndex = 0;
    
    // 如果需要类型转换
    if (currentType !== targetType) {
        const typeTransformNode = new Node('normal');
        typeTransformNode.id = `transform_type_${pathIndex}_${nodeIndex++}`;
        
        // 输入端口：接收当前类型和颜色
        typeTransformNode.addInputPort(currentType, currentColor);
        
        // 输出端口：输出目标类型，保持颜色
        typeTransformNode.addOutputPort(targetType, currentColor);
        
        typeTransformNode.label = `[${currentType}:${currentColor}]→[${targetType}:${currentColor}]`;
        
        pathNodes.push(typeTransformNode);
        currentType = targetType;
    }
    
    // 如果需要颜色转换
    if (currentColor !== targetColor) {
        const colorTransformNode = new Node('normal');
        colorTransformNode.id = `transform_color_${pathIndex}_${nodeIndex++}`;
        
        // 输入端口：接收当前类型和颜色
        colorTransformNode.addInputPort(currentType, currentColor);
        
        // 输出端口：输出相同类型，目标颜色
        colorTransformNode.addOutputPort(currentType, targetColor);
        
        colorTransformNode.label = `[${currentType}:${currentColor}]→[${currentType}:${targetColor}]`;
        
        pathNodes.push(colorTransformNode);
        currentColor = targetColor;
    }
    
    // 如果路径太短，添加一个或多个中间节点来增加复杂性
    if (pathNodes.length === 0 && difficulty.level > 2) {
        // 创建第一个中间节点：source -> intermediate
        const intermediateNode1 = new Node('normal');
        intermediateNode1.id = `intermediate_${pathIndex}_${nodeIndex++}`;
        
        // 选择一个中间状态
        const intermediateType = getIntermediateType(source.type, target.type, difficulty.availableTypes);
        const intermediateColor = getIntermediateColor(source.color, target.color, difficulty.availableColors);
        
        intermediateNode1.addInputPort(source.type, source.color);
        intermediateNode1.addOutputPort(intermediateType, intermediateColor);
        intermediateNode1.label = `[${source.type}:${source.color}]→[${intermediateType}:${intermediateColor}]`;
        
        pathNodes.push(intermediateNode1);
        
        // 对于高难度，添加第二个中间节点：intermediate -> target
        if (difficulty.level > 5) {
            const intermediateNode2 = new Node('normal');
            intermediateNode2.id = `intermediate_${pathIndex}_${nodeIndex++}`;
            
            intermediateNode2.addInputPort(intermediateType, intermediateColor);
            intermediateNode2.addOutputPort(target.type, target.color);
            intermediateNode2.label = `[${intermediateType}:${intermediateColor}]→[${target.type}:${target.color}]`;
            
            pathNodes.push(intermediateNode2);
        } else {
            // 低难度时，直接从中间状态到目标
            const finalNode = new Node('normal');
            finalNode.id = `final_${pathIndex}_${nodeIndex++}`;
            
            finalNode.addInputPort(intermediateType, intermediateColor);
            finalNode.addOutputPort(target.type, target.color);
            finalNode.label = `[${intermediateType}:${intermediateColor}]→[${target.type}:${target.color}]`;
            
            pathNodes.push(finalNode);
        }
    }
    
    return {
        nodes: pathNodes,
        connections: connections
    };
}

// 生成额外节点以增加复杂性 - 改进版本
function generateExtraNodes(existingNodes, difficulty) {
    const extraNodes = [];
    const extraCount = Math.min(Math.floor(difficulty.nodes * 0.4), 3); // 限制额外节点数量
    
    // 收集现有节点的端口信息
    const availableOutputs = [];
    const availableInputs = [];
    
    existingNodes.forEach(node => {
        if (node.outputPorts) {
            node.outputPorts.forEach(port => {
                availableOutputs.push({ type: port.type, color: port.color });
            });
        }
        if (node.inputPorts) {
            node.inputPorts.forEach(port => {
                availableInputs.push({ type: port.type, color: port.color });
            });
        }
    });
    
    for (let i = 0; i < extraCount && availableOutputs.length > 0 && availableInputs.length > 0; i++) {
        const node = new Node('normal');
        node.id = `extra_node_${i + 1}`;
        
        // 选择一个可以连接到现有输出的输入端口
        const inputChoice = availableOutputs[Math.floor(Math.random() * availableOutputs.length)];
        node.addInputPort(inputChoice.type, inputChoice.color);
        
        // 选择一个现有节点需要的输出端口，或创建新的组合
        let outputChoice;
        if (Math.random() < 0.7 && availableInputs.length > 0) {
            // 70%的概率选择现有需要的端口
            outputChoice = availableInputs[Math.floor(Math.random() * availableInputs.length)];
        } else {
            // 30%的概率创建新的端口组合
            outputChoice = {
                type: difficulty.availableTypes[Math.floor(Math.random() * difficulty.availableTypes.length)],
                color: difficulty.availableColors[Math.floor(Math.random() * difficulty.availableColors.length)]
            };
        }
        
        node.addOutputPort(outputChoice.type, outputChoice.color);
        node.label = `Extra[${inputChoice.type}:${inputChoice.color.substr(1,2)}]→[${outputChoice.type}:${outputChoice.color.substr(1,2)}]`;
        
        extraNodes.push(node);
        
        // 更新可用端口列表
        availableOutputs.push({ type: outputChoice.type, color: outputChoice.color });
    }
    
    return extraNodes;
}

// 辅助函数
function getAlternativeType(currentType, availableTypes) {
    const alternatives = availableTypes.filter(type => type !== currentType);
    return alternatives[Math.floor(Math.random() * alternatives.length)] || currentType;
}

function getAlternativeColor(currentColor, availableColors) {
    const alternatives = availableColors.filter(color => color !== currentColor);
    return alternatives[Math.floor(Math.random() * alternatives.length)] || currentColor;
}

function getIntermediateType(sourceType, targetType, availableTypes) {
    const alternatives = availableTypes.filter(type => type !== sourceType && type !== targetType);
    return alternatives[Math.floor(Math.random() * alternatives.length)] || sourceType;
}

function getIntermediateColor(sourceColor, targetColor, availableColors) {
    const alternatives = availableColors.filter(color => color !== sourceColor && color !== targetColor);
    return alternatives[Math.floor(Math.random() * alternatives.length)] || sourceColor;
}

function createImprovedStartNode(provisions) {
    const node = new Node('start');
    node.id = 'start_node';
    
    provisions.forEach(provision => {
        node.addOutputPort(provision.type, provision.color);
    });
    
    node.label = 'Start';
    return node;
}

function createImprovedEndNode(requirements) {
    const node = new Node('end');
    node.id = 'end_node';
    
    requirements.forEach(requirement => {
        node.addInputPort(requirement.type, requirement.color);
    });
    
    node.label = 'End';
    return node;
}

// 生成严格可解的节点集合 (保留原函数以兼容其他调用)
function generateStrictlySolvableNodeSet() {
    // 第一步：定义终点需求（这是我们的目标）
    const endRequirements = generateEndRequirements();
    
    // 第二步：定义起点提供（确保数量满足一对一约束）
    const startProvisions = generateStartProvisions(endRequirements);
    
    // 第三步：分析转换需求，设计最小转换链
    const transformationChain = designMinimalTransformationChain(startProvisions, endRequirements);
    
    // 第四步：创建具体的节点
    const startNode = createStartNode(startProvisions);
    const endNode = createEndNode(endRequirements);
    const requiredNodes = createTransformationNodes(transformationChain);
    
    return {
        startNode,
        endNode,
        requiredNodes,
        solution: transformationChain
    };
}

// 生成基于关卡难度的节点集合
function generateLevelBasedNodeSet(difficulty) {
    const nodeSet = {
        startNode: null,
        endNode: null,
        startNodes: [],
        endNodes: [],
        requiredNodes: [],
        solution: []
    };
    
    // 根据难度生成更复杂的需求
    const endRequirements = generateComplexEndRequirements(difficulty);
    const startProvisions = generateMatchingStartProvisions(endRequirements, difficulty);
    
    // 生成转换链（支持同类型多节点）
    const transformationChains = generateAdvancedTransformationChains(startProvisions, endRequirements, difficulty);
    
    // 创建起点和终点节点（可能是单个或多个）
    const startNodes = createStartNode(startProvisions);
    const endNodes = createEndNode(endRequirements);
    
    // 处理单个或多个节点的情况
    nodeSet.startNodes = Array.isArray(startNodes) ? startNodes : [startNodes];
    nodeSet.endNodes = Array.isArray(endNodes) ? endNodes : [endNodes];
    nodeSet.startNode = nodeSet.startNodes[0]; // 主要起点（向后兼容）
    nodeSet.endNode = nodeSet.endNodes[0]; // 主要终点（向后兼容）
    
    nodeSet.requiredNodes = createAdvancedTransformationNodes(transformationChains, difficulty);
    nodeSet.solution = transformationChains;
    
    return nodeSet;
}

// 生成基础起点和终点（俄罗斯方块模式）
function generateBasicStartEndNodes(difficulty) {
    const endRequirements = generateComplexEndRequirements(difficulty);
    const startProvisions = generateMatchingStartProvisions(endRequirements, difficulty);
    
    const solution = generateAdvancedTransformationChains(startProvisions, endRequirements, difficulty);
    
    return {
        startNode: createStartNode(startProvisions),
        endNode: createEndNode(endRequirements),
        solution: solution
    };
}

// 生成俄罗斯方块节点队列
function generateTetrisNodeQueue(difficulty, count) {
    const nodeQueue = [];
    const availableTypes = gameState.portTypes.slice(0, difficulty.types);
    const availableColors = gameState.portColors.slice(0, difficulty.colors);
    
    for (let i = 0; i < count; i++) {
        const node = new Node(50, 50, 'normal');
        
        // 随机生成输入和输出端口
        const inputType = availableTypes[Math.floor(Math.random() * availableTypes.length)];
        const inputColor = availableColors[Math.floor(Math.random() * availableColors.length)];
        const outputType = availableTypes[Math.floor(Math.random() * availableTypes.length)];
        const outputColor = availableColors[Math.floor(Math.random() * availableColors.length)];
        
        node.addInputPort(inputType, inputColor);
        node.addOutputPort(outputType, outputColor);
        node.label = `${inputType}→${outputType}`;
        
        nodeQueue.push(node);
    }
    
    return nodeQueue;
}

// 开始俄罗斯方块掉落循环
function startTetrisDropLoop() {
    if (!gameState.tetrisMode.isActive) return;
    
    const now = Date.now();
    
    // 更新剩余时间
    if (gameState.levelConfig.currentLevelStartTime) {
        const elapsed = now - gameState.levelConfig.currentLevelStartTime;
        const remaining = gameState.levelConfig.levelTimeLimit * 1000 - elapsed;
        gameState.tetrisMode.timeRemaining = Math.max(0, Math.floor(remaining / 1000));
        
        if (remaining <= 0) {
            // 时间到，游戏结束
            endTetrisLevel(false, '⏰ 时间到！关卡失败');
            return;
        }
    }
    
    // 检查是否到了掉落时间
    if (now - gameState.tetrisMode.lastDropTime >= gameState.tetrisMode.dropInterval) {
        dropNextTetrisNode();
        gameState.tetrisMode.lastDropTime = now;
    }
    
    // 继续循环
    setTimeout(startTetrisDropLoop, 100);
}

// 投放下一个俄罗斯方块节点
function dropNextTetrisNode() {
    if (gameState.tetrisMode.nodeQueue.length === 0) {
        // 队列为空，检查是否可以胜利
        checkTetrisVictoryCondition();
        return;
    }
    
    // 检查临时区域是否已满
    if (gameState.temporaryNodes.length >= gameState.temporaryArea.maxNodes) {
        gameState.tetrisMode.overflowWarnings++;
        
        if (gameState.tetrisMode.overflowWarnings >= gameState.tetrisMode.maxOverflowWarnings) {
            // 溢出次数过多，游戏结束
            endTetrisLevel(false, '临时区域溢出过多次！');
            return;
        } else {
            // 警告玩家临时区域快满了
            updateMessage(`⚠️ 临时区域已满！清理节点或连接蓝图！警告: ${gameState.tetrisMode.overflowWarnings}/${gameState.tetrisMode.maxOverflowWarnings}`);
            
            // 延迟下一次掉落
            gameState.tetrisMode.lastDropTime = Date.now() + 3000; // 额外延迟3秒
            return;
        }
    }
    
    const node = gameState.tetrisMode.nodeQueue.shift();
    
    // 在临时区域的顶部投放节点，避免重叠
    const maxAttempts = 10;
    let x, y;
    let validPosition = false;
    
    for (let attempt = 0; attempt < maxAttempts; attempt++) {
        x = gameState.temporaryArea.x + 20 + Math.random() * (gameState.temporaryArea.width - 140);
        y = gameState.temporaryArea.y + 20;
        
        // 检查是否与现有节点重叠
        const overlap = gameState.temporaryNodes.some(existingNode => {
            const dx = Math.abs(existingNode.x - x);
            const dy = Math.abs(existingNode.y - y);
            return dx < 130 && dy < 70; // 节点大小 + 一点间距
        });
        
        if (!overlap) {
            validPosition = true;
            break;
        }
        
        // 如果重叠，尝试下一行
        y += 80;
        if (y > gameState.temporaryArea.y + gameState.temporaryArea.height - 80) {
            break;
        }
    }
    
    if (!validPosition) {
        // 找不到有效位置，强制覆盖最老的位置
        x = gameState.temporaryArea.x + 20;
        y = gameState.temporaryArea.y + 20;
    }
    
    node.moveTo(x, y);
    gameState.temporaryNodes.push(node);
    
    // 更新下一个节点预览
    gameState.tetrisMode.nextNodePreview = gameState.tetrisMode.nodeQueue[0] || null;
    
    const capacityInfo = `${gameState.temporaryNodes.length}/${gameState.temporaryArea.maxNodes}`;
    updateMessage(`🎯 新节点投放！容量: ${capacityInfo}, 剩余: ${gameState.tetrisMode.nodeQueue.length}`);
    
    // 检查胜利条件（每次投放后都检查）
    setTimeout(() => checkTetrisVictoryCondition(), 500);
}

// 检查俄罗斯方块模式胜利条件
function checkTetrisVictoryCondition() {
    if (!gameState.tetrisMode.isActive) return;
    
    // 检查蓝图是否已经正确连接
    const validation = validateBlueprint();
    if (validation.isValid) {
        // 胜利！
        endTetrisLevel(true, '🎉 蓝图连接正确！关卡完成！');
        return true;
    }
    
    // 检查是否还有可能完成（队列空了但蓝图不完整）
    if (gameState.tetrisMode.nodeQueue.length === 0) {
        // 队列空了，但蓝图未完成
        const canStillSolve = checkIfCanStillSolve();
        if (!canStillSolve) {
            endTetrisLevel(false, '❌ 无法完成蓝图！节点不足或配置错误');
            return false;
        }
    }
    
    return false;
}

// 检查当前状态是否还有可能解决蓝图
function checkIfCanStillSolve() {
    // 简单检查：看临时区域和摆放区域是否有足够的节点来完成连接
    const allAvailableNodes = [...gameState.temporaryNodes, ...gameState.placedNodes];
    
    // 至少需要有起点和终点
    const startNodes = allAvailableNodes.filter(node => node.type === 'start');
    const endNodes = allAvailableNodes.filter(node => node.type === 'end');
    
    if (startNodes.length === 0 || endNodes.length === 0) {
        return false; // 没有起点或终点
    }
    
    // 可以增加更复杂的检查逻辑
    return true;
}

// 结束俄罗斯方块关卡
function endTetrisLevel(success, reason = '') {
    gameState.tetrisMode.isActive = false;
    
    if (success) {
        updateMessage(reason || '🎉 关卡完成！');
        completeLevel();
    } else {
        updateMessage(reason || '⏰ 时间到！关卡失败');
        // 可以选择重新开始或回到菜单
        setTimeout(() => {
            if (gameState.level > 1) {
                gameState.level = Math.max(1, gameState.level - 1);
            }
            generatePuzzleLevel();
        }, 3000);
    }
}

// ===========================================
// 多起终点节点支持 (Multiple Start/End Nodes)
// ===========================================

// 生成多起点/终点场景
function generateMultiNodeScenario() {
    const level = gameState.level;
    const difficulty = Math.min(level, 10);
    
    // 决定是否启用多起点/终点（基于关卡难度）
    gameState.multiNodeConfig.enableMultipleStarts = level >= 5 && Math.random() > 0.5;
    gameState.multiNodeConfig.enableMultipleEnds = level >= 7 && Math.random() > 0.5;
    
    // 清空现有节点
    gameState.placedNodes = [];
    gameState.temporaryNodes = [];
    gameState.connections = [];
    gameState.multiNodeConfig.currentStartNodes = [];
    gameState.multiNodeConfig.currentEndNodes = [];
    
    // 生成起点节点
    const startNodes = generateMultipleStartNodes(difficulty);
    gameState.multiNodeConfig.currentStartNodes = startNodes;
    gameState.placedNodes.push(...startNodes);
    
    // 生成终点节点
    const endNodes = generateMultipleEndNodes(difficulty);
    gameState.multiNodeConfig.currentEndNodes = endNodes;
    gameState.placedNodes.push(...endNodes);
    
    // 生成中间转换节点
    const transformNodes = generateTransformNodesForMultiPath(startNodes, endNodes, difficulty);
    gameState.temporaryNodes.push(...transformNodes);
    
    updateMessage(`🔀 多路径蓝图 - 起点: ${startNodes.length}, 终点: ${endNodes.length}`);
    
    return {
        startNodes,
        endNodes,
        transformNodes,
        complexity: startNodes.length * endNodes.length
    };
}

// 生成多个起点节点
function generateMultipleStartNodes(difficulty) {
    const startNodes = [];
    const startCount = gameState.multiNodeConfig.enableMultipleStarts ? 
        Math.min(2 + Math.floor(difficulty / 3), gameState.multiNodeConfig.maxStartNodes) : 1;
    
    const portTypes = gameState.portTypes.slice(0, Math.min(4, 2 + difficulty));
    const portColors = gameState.portColors.slice(0, Math.min(4, 2 + difficulty));
    
    for (let i = 0; i < startCount; i++) {
        const startNode = new Node('start');
        startNode.label = startCount > 1 ? `Start-${i + 1}` : 'Start';
        
        // 为每个起点节点添加不同类型的输出端口
        const outputCount = Math.min(3, 1 + Math.floor(difficulty / 2));
        for (let j = 0; j < outputCount; j++) {
            const type = portTypes[(i + j) % portTypes.length];
            const color = portColors[(i + j) % portColors.length];
            startNode.addOutputPort(type, color);
        }
        
        // 位置排列
        const x = 50;
        const y = 100 + i * 120;
        startNode.moveTo(x, y);
        
        startNodes.push(startNode);
    }
    
    return startNodes;
}

// 生成多个终点节点
function generateMultipleEndNodes(difficulty) {
    const endNodes = [];
    const endCount = gameState.multiNodeConfig.enableMultipleEnds ? 
        Math.min(2 + Math.floor(difficulty / 4), gameState.multiNodeConfig.maxEndNodes) : 1;
    
    const portTypes = gameState.portTypes.slice(0, Math.min(4, 2 + difficulty));
    const portColors = gameState.portColors.slice(0, Math.min(4, 2 + difficulty));
    
    for (let i = 0; i < endCount; i++) {
        const endNode = new Node('end');
        endNode.label = endCount > 1 ? `End-${i + 1}` : 'End';
        
        // 为每个终点节点添加不同类型的输入端口
        const inputCount = Math.min(3, 1 + Math.floor(difficulty / 2));
        for (let j = 0; j < inputCount; j++) {
            const type = portTypes[(i + j + 1) % portTypes.length];
            const color = portColors[(i + j + 1) % portColors.length];
            endNode.addInputPort(type, color);
        }
        
        // 位置排列
        const x = 550;
        const y = 100 + i * 120;
        endNode.moveTo(x, y);
        
        endNodes.push(endNode);
    }
    
    return endNodes;
}

// 为多路径生成转换节点
function generateTransformNodesForMultiPath(startNodes, endNodes, difficulty) {
    const transformNodes = [];
    const allStartPorts = startNodes.flatMap(node => node.outputPorts);
    const allEndPorts = endNodes.flatMap(node => node.inputPorts);
    
    // 确保每个终点都能从至少一个起点到达
    const requiredTransforms = [];
    
    endNodes.forEach((endNode, endIndex) => {
        endNode.inputPorts.forEach((endPort, portIndex) => {
            // 为每个终点端口找到对应的起点端口
            const compatibleStartPorts = allStartPorts.filter(startPort => 
                startPort.type !== endPort.type || startPort.color !== endPort.color
            );
            
            if (compatibleStartPorts.length > 0) {
                const sourcePort = compatibleStartPorts[Math.floor(Math.random() * compatibleStartPorts.length)];
                
                // 创建转换节点
                const transformNode = new Node('normal');
                transformNode.label = `T${endIndex + 1}-${portIndex + 1}`;
                
                transformNode.addInputPort(sourcePort.type, sourcePort.color);
                transformNode.addOutputPort(endPort.type, endPort.color);
                
                // 位置在起点和终点之间
                const x = 200 + Math.random() * 150;
                const y = 80 + (endIndex * 120) + (portIndex * 40);
                transformNode.moveTo(x, y);
                
                requiredTransforms.push(transformNode);
            }
        });
    });
    
    transformNodes.push(...requiredTransforms);
    
    // 添加一些额外的干扰节点
    const distractorCount = Math.floor(difficulty / 2);
    for (let i = 0; i < distractorCount; i++) {
        const distractorNode = generateRandomNode();
        distractorNode.label = `Distractor-${i + 1}`;
        
        const x = 200 + Math.random() * 150;
        const y = 400 + i * 80;
        distractorNode.moveTo(x, y);
        
        transformNodes.push(distractorNode);
    }
    
    return transformNodes;
}

// 验证多路径蓝图
function validateMultiPathBlueprint() {
    const startNodes = gameState.multiNodeConfig.currentStartNodes;
    const endNodes = gameState.multiNodeConfig.currentEndNodes;
    
    if (startNodes.length === 0 || endNodes.length === 0) {
        return { isValid: false, message: '缺少起点或终点节点', paths: [] };
    }
    
    const validPaths = [];
    const allPathsComplete = [];
    
    // 检查每个起点到终点的路径
    startNodes.forEach(startNode => {
        endNodes.forEach(endNode => {
            const paths = findAllValidPaths(startNode, endNode);
            if (paths.length > 0) {
                validPaths.push({ start: startNode, end: endNode, paths });
                allPathsComplete.push(true);
            } else {
                allPathsComplete.push(false);
            }
        });
    });
    
    // 检查是否满足完成要求
    const requireAllPaths = gameState.multiNodeConfig.requireAllPathsComplete;
    const isValid = requireAllPaths ? 
        allPathsComplete.every(complete => complete) : 
        allPathsComplete.some(complete => complete);
    
    return {
        isValid,
        message: isValid ? 
            `✅ 多路径验证成功！完成路径: ${validPaths.length}` :
            `❌ 多路径验证失败！需要完成更多路径连接`,
        paths: validPaths,
        totalPaths: startNodes.length * endNodes.length,
        completedPaths: validPaths.length
    };
}

// 切换多起点模式
function toggleMultipleStartNodes() {
    gameState.multiNodeConfig.enableMultipleStarts = !gameState.multiNodeConfig.enableMultipleStarts;
    
    const message = gameState.multiNodeConfig.enableMultipleStarts ? 
        '🔄 启用多起点模式' : '🔄 禁用多起点模式';
    updateMessage(message);
    
    // 如果当前是puzzle模式，重新生成关卡
    if (gameState.mode === 'puzzle') {
        generateMultiNodeScenario();
    }
}

// 切换多终点模式
function toggleMultipleEndNodes() {
    gameState.multiNodeConfig.enableMultipleEnds = !gameState.multiNodeConfig.enableMultipleEnds;
    
    const message = gameState.multiNodeConfig.enableMultipleEnds ? 
        '🎯 启用多终点模式' : '🎯 禁用多终点模式';
    updateMessage(message);
    
    // 如果当前是puzzle模式，重新生成关卡
    if (gameState.mode === 'puzzle') {
        generateMultiNodeScenario();
    }
}

// 更新多节点按钮状态
function updateMultiNodeButtonStates() {
    const multiStartBtn = document.getElementById('multi-start-toggle-btn');
    const multiEndBtn = document.getElementById('multi-end-toggle-btn');
    
    if (multiStartBtn) {
        if (gameState.multiNodeConfig.enableMultipleStarts) {
            multiStartBtn.classList.add('active');
            multiStartBtn.textContent = '🔄 多起点 (启用)';
        } else {
            multiStartBtn.classList.remove('active');
            multiStartBtn.innerHTML = '🔄 多起点 <kbd>U</kbd>';
        }
    }
    
    if (multiEndBtn) {
        if (gameState.multiNodeConfig.enableMultipleEnds) {
            multiEndBtn.classList.add('active');
            multiEndBtn.textContent = '🎯 多终点 (启用)';
        } else {
            multiEndBtn.classList.remove('active');
            multiEndBtn.innerHTML = '🎯 多终点 <kbd>I</kbd>';
        }
    }
}

// ===========================================
// 无限构筑模式 (Infinite Construction Mode)
// ===========================================

// 开始无限构筑模式
function startInfiniteLevel() {
    const level = gameState.level;
    
    // 清空现有节点（但保留基础蓝图的一部分）
    gameState.connections = [];
    
    // 初始化无限模式状态
    gameState.infiniteMode.isActive = true;
    gameState.infiniteMode.currentWave = 1;
    gameState.infiniteMode.completedWaves = 0;
    gameState.infiniteMode.lastWaveChangeTime = Date.now();
    gameState.infiniteMode.accumulatedScore = 0;
    gameState.infiniteMode.continuousSolutions = 0;
    gameState.infiniteMode.adaptiveDifficulty = Math.max(1, Math.floor(level / 2));
    
    // 如果有基础蓝图，从中构建
    if (gameState.infiniteMode.baseBlueprint && gameState.infiniteMode.baseBlueprint.nodes) {
        // 保留部分基础节点
        gameState.placedNodes = [...gameState.infiniteMode.baseBlueprint.nodes];
        if (gameState.infiniteMode.baseBlueprint.connections) {
            gameState.connections = [...gameState.infiniteMode.baseBlueprint.connections];
        }
    } else {
        // 创建初始基础蓝图
        generateInitialInfiniteBlueprint();
    }
    
    // 为当前波次生成新的要求
    generateWaveRequirements();
    
    // 开始波次循环
    startInfiniteWaveLoop();
    
    console.log(`🌊 启动无限构筑模式 - 波次 ${gameState.infiniteMode.currentWave}`, {
        难度: gameState.infiniteMode.adaptiveDifficulty,
        波次间隔: gameState.infiniteMode.waveChangeInterval + 'ms',
        当前要求数: gameState.infiniteMode.currentRequirements.length
    });
    
    updateMessage(`🌊 无限构筑模式 - 波次 ${gameState.infiniteMode.currentWave} 开始！`);
}

// 生成初始的无限模式蓝图
function generateInitialInfiniteBlueprint() {
    gameState.temporaryNodes = [];
    gameState.placedNodes = [];
    
    // 创建基础的起点和终点
    const startNode = new Node('start');
    const endNode = new Node('end');
    
    // 基础配置：简单的输入输出
    startNode.addOutputPort('square', '#ff5252');
    endNode.addInputPort('square', '#ff5252');
    
    // 位置设置
    startNode.moveTo(50, 200);
    endNode.moveTo(550, 200);
    
    gameState.placedNodes.push(startNode, endNode);
    
    // 保存为基础蓝图
    gameState.infiniteMode.baseBlueprint = {
        nodes: [startNode.clone(), endNode.clone()],
        connections: []
    };
}

// 生成当前波次的新要求
function generateWaveRequirements() {
    const wave = gameState.infiniteMode.currentWave;
    const difficulty = gameState.infiniteMode.adaptiveDifficulty;
    
    // 清空之前的要求
    gameState.infiniteMode.currentRequirements = [];
    
    // 根据波次和难度生成新的端口要求
    const requirementCount = Math.min(wave, 4) + Math.floor(difficulty / 2);
    const availableTypes = ['square', 'circle', 'triangle', 'diamond'];
    const availableColors = ['#ff5252', '#2196F3', '#4CAF50', '#FFC107'];
    
    for (let i = 0; i < requirementCount; i++) {
        const requirement = {
            id: `wave${wave}_req${i + 1}`,
            type: availableTypes[i % availableTypes.length],
            color: availableColors[i % availableColors.length],
            sourceType: availableTypes[(i + 1) % availableTypes.length],
            sourceColor: availableColors[(i + 1) % availableColors.length],
            priority: Math.random() > 0.5 ? 'high' : 'normal'
        };
        
        gameState.infiniteMode.currentRequirements.push(requirement);
    }
    
    // 动态修改终点节点的需求
    updateEndNodeRequirements();
    
    // 在临时区域生成对应的节点
    generateRequirementNodes();
}

// 更新终点节点的需求
function updateEndNodeRequirements() {
    const endNode = gameState.placedNodes.find(node => node.type === 'end');
    if (!endNode) return;
    
    // 清空现有的输入端口
    endNode.inputPorts = [];
    
    // 根据新要求添加输入端口
    gameState.infiniteMode.currentRequirements.forEach(req => {
        endNode.addInputPort(req.type, req.color);
    });
    
    // 更新端口位置
    endNode.updatePortPositions();
}

// 生成满足要求的节点
function generateRequirementNodes() {
    const requirements = gameState.infiniteMode.currentRequirements;
    
    // 清空临时区域
    gameState.temporaryNodes = [];
    
    requirements.forEach((req, index) => {
        // 创建转换节点
        const transformNode = new Node('normal');
        transformNode.label = `Wave${gameState.infiniteMode.currentWave}-${index + 1}`;
        
        // 添加输入和输出端口
        transformNode.addInputPort(req.sourceType, req.sourceColor);
        transformNode.addOutputPort(req.type, req.color);
        
        // 放置在临时区域
        const y = 50 + (index * 90);
        transformNode.moveTo(20, y);
        
        gameState.temporaryNodes.push(transformNode);
    });
    
    // 添加一些干扰节点
    const distractorCount = Math.floor(gameState.infiniteMode.adaptiveDifficulty / 2);
    for (let i = 0; i < distractorCount; i++) {
        const distractorNode = generateRandomNode();
        distractorNode.label = `Distractor-${i + 1}`;
        
        const y = 50 + ((requirements.length + i) * 90);
        distractorNode.moveTo(20, y);
        
        gameState.temporaryNodes.push(distractorNode);
    }
}

// 开始无限模式波次循环
function startInfiniteWaveLoop() {
    if (!gameState.infiniteMode.isActive) return;
    
    const now = Date.now();
    const timeSinceLastWave = now - gameState.infiniteMode.lastWaveChangeTime;
    
    // 检查是否该进入下一波
    if (timeSinceLastWave >= gameState.infiniteMode.waveChangeInterval) {
        advanceToNextWave();
    }
    
    // 检查当前波次的完成情况
    checkInfiniteWaveCompletion();
    
    // 继续循环
    setTimeout(startInfiniteWaveLoop, 1000);
}

// 推进到下一波
function advanceToNextWave() {
    // 保存当前蓝图状态
    saveCurrentBlueprintState();
    
    gameState.infiniteMode.currentWave++;
    gameState.infiniteMode.lastWaveChangeTime = Date.now();
    
    // 检查是否达到最大波次
    if (gameState.infiniteMode.currentWave > gameState.infiniteMode.maxWaves) {
        endInfiniteLevel(true, '🎉 完成所有波次！无限构筑大师！');
        return;
    }
    
    // 增加自适应难度
    if (gameState.infiniteMode.continuousSolutions >= 3) {
        gameState.infiniteMode.adaptiveDifficulty++;
        gameState.infiniteMode.continuousSolutions = 0;
        updateMessage(`💪 难度提升！当前难度: ${gameState.infiniteMode.adaptiveDifficulty}`);
    }
    
    // 生成新波次的要求
    generateWaveRequirements();
    
    updateMessage(`🌊 波次 ${gameState.infiniteMode.currentWave} 开始！新的挑战来临！`);
}

// 保存当前蓝图状态
function saveCurrentBlueprintState() {
    const currentState = {
        wave: gameState.infiniteMode.currentWave,
        nodes: gameState.placedNodes.map(node => node.clone()),
        connections: gameState.connections.map(conn => ({
            fromPortId: conn.fromPort.id,
            toPortId: conn.toPort.id,
            color: conn.color
        })),
        score: gameState.score,
        timestamp: Date.now()
    };
    
    gameState.infiniteMode.blueprintHistory.push(currentState);
    
    // 更新基础蓝图（保留当前的成功构建）
    gameState.infiniteMode.baseBlueprint = {
        nodes: currentState.nodes,
        connections: currentState.connections
    };
}

// 检查无限模式波次完成情况
function checkInfiniteWaveCompletion() {
    if (!gameState.infiniteMode.isActive) return;
    
    // 检查当前蓝图是否满足要求
    const validation = validateBlueprint();
    if (validation.isValid) {
        // 当前波次完成
        completeInfiniteWave();
    }
}

// 完成当前无限模式波次
function completeInfiniteWave() {
    gameState.infiniteMode.completedWaves++;
    gameState.infiniteMode.continuousSolutions++;
    
    // 计算波次奖励分数
    const waveBonus = gameState.infiniteMode.currentWave * 100 * gameState.infiniteMode.adaptiveDifficulty;
    gameState.infiniteMode.accumulatedScore += waveBonus;
    gameState.score += waveBonus;
    
    updateMessage(`✅ 波次 ${gameState.infiniteMode.currentWave} 完成！奖励: ${waveBonus}分`);
    
    // 立即推进到下一波
    setTimeout(() => advanceToNextWave(), 2000);
}

// 结束无限构筑模式
function endInfiniteLevel(success, reason = '') {
    gameState.infiniteMode.isActive = false;
    
    if (success) {
        updateMessage(reason || '🎉 无限构筑完成！');
        
        // 计算最终分数
        const finalBonus = gameState.infiniteMode.completedWaves * 500;
        gameState.score += finalBonus;
        
        completeLevel();
    } else {
        updateMessage(reason || '💥 无限构筑失败！');
        
        // 可以重新开始或回到菜单
        setTimeout(() => {
            generatePuzzleLevel();
        }, 3000);
    }
}

// 播放关卡完成动画
function playLevelCompleteAnimation() {
    // 简单的成功动画效果
    const message = document.getElementById('message');
    if (message) {
        message.style.animation = 'pulse 1s ease-in-out';
        setTimeout(() => {
            message.style.animation = '';
        }, 1000);
    }
    
    // 可以添加更多动画效果
    console.log('🎉 播放关卡完成动画');
}

// 更新关卡显示
function updateLevelDisplay() {
    const levelElement = document.getElementById('level-counter');
    if (levelElement) {
        levelElement.textContent = gameState.level;
    }
    
    // 更新分数显示
    const scoreElement = document.getElementById('score-counter');
    if (scoreElement) {
        scoreElement.textContent = gameState.score;
    }
    
    // 更新模式显示
    const modeElement = document.getElementById('mode-display');
    if (modeElement) {
        const modeText = gameState.mode === 'puzzle' ? '谜题' : 
                        gameState.mode === 'tetris' ? '俄罗斯方块' : 
                        gameState.mode === 'infinite' ? '无限构筑' : '经典';
        modeElement.textContent = modeText;
    }
    
    // 更新Tetris模式信息显示
    updateTetrisInfo();
}

// 更新Tetris模式信息显示
function updateTetrisInfo() {
    const tetrisInfo = document.getElementById('tetris-info');
    
    if (gameState.mode === 'tetris') {
        // 显示Tetris信息
        if (tetrisInfo) {
            tetrisInfo.style.display = 'block';
            
            // 更新剩余时间
            const timeElement = document.getElementById('time-remaining');
            if (timeElement) {
                timeElement.textContent = gameState.tetrisMode.timeRemaining;
                // 时间不足时变红
                if (gameState.tetrisMode.timeRemaining <= 30) {
                    timeElement.style.color = '#ff4444';
                } else if (gameState.tetrisMode.timeRemaining <= 60) {
                    timeElement.style.color = '#ffaa00';
                } else {
                    timeElement.style.color = '#ffffff';
                }
            }
            
            // 更新容量信息
            const capacityElement = document.getElementById('temp-capacity');
            if (capacityElement) {
                const current = gameState.temporaryNodes.length;
                const max = gameState.temporaryArea.maxNodes;
                capacityElement.textContent = `${current}/${max}`;
                
                // 容量快满时变红
                if (current >= max) {
                    capacityElement.style.color = '#ff4444';
                } else if (current >= max - 1) {
                    capacityElement.style.color = '#ffaa00';
                } else {
                    capacityElement.style.color = '#ffffff';
                }
            }
            
            // 更新队列信息
            const queueElement = document.getElementById('queue-count');
            if (queueElement) {
                queueElement.textContent = gameState.tetrisMode.nodeQueue.length;
            }
            
            // 更新下一个节点预览
            const nextNodeElement = document.getElementById('next-node-preview');
            if (nextNodeElement) {
                if (gameState.tetrisMode.nextNodePreview) {
                    const node = gameState.tetrisMode.nextNodePreview;
                    const portCount = node.inputPorts.length + node.outputPorts.length;
                    nextNodeElement.textContent = `下一个: ${portCount}端口节点`;
                } else {
                    nextNodeElement.textContent = '下一个: 无';
                }
            }
        }
    } else {
        // 隐藏Tetris信息
        if (tetrisInfo) {
            tetrisInfo.style.display = 'none';
        }
    }
    
    // 更新Infinite模式信息
    updateInfiniteInfo();
    
    // 更新模式按钮状态
    updateModeButtonStates();
}

// 更新Infinite模式信息显示
function updateInfiniteInfo() {
    const infiniteInfo = document.getElementById('infinite-info');
    
    if (gameState.mode === 'infinite') {
        // 显示Infinite信息
        if (infiniteInfo) {
            infiniteInfo.style.display = 'block';
            
            // 更新当前波次
            const currentWaveElement = document.getElementById('current-wave');
            if (currentWaveElement) {
                currentWaveElement.textContent = gameState.infiniteMode.currentWave;
            }
            
            // 更新最大波次
            const maxWavesElement = document.getElementById('max-waves');
            if (maxWavesElement) {
                maxWavesElement.textContent = gameState.infiniteMode.maxWaves;
            }
            
            // 更新完成波次
            const completedWavesElement = document.getElementById('completed-waves');
            if (completedWavesElement) {
                completedWavesElement.textContent = gameState.infiniteMode.completedWaves;
            }
            
            // 更新自适应难度
            const difficultyElement = document.getElementById('adaptive-difficulty');
            if (difficultyElement) {
                difficultyElement.textContent = gameState.infiniteMode.adaptiveDifficulty;
            }
            
            // 更新波次计时器
            const waveTimerElement = document.getElementById('wave-timer');
            if (waveTimerElement && gameState.infiniteMode.isActive) {
                const now = Date.now();
                const elapsed = now - gameState.infiniteMode.lastWaveChangeTime;
                const remaining = Math.max(0, Math.floor((gameState.infiniteMode.waveChangeInterval - elapsed) / 1000));
                
                waveTimerElement.textContent = remaining;
                
                // 时间不足时变红
                if (remaining <= 5) {
                    waveTimerElement.style.color = '#ff4444';
                } else if (remaining <= 10) {
                    waveTimerElement.style.color = '#ffaa00';
                } else {
                    waveTimerElement.style.color = '#ffffff';
                }
            }
        }
    } else {
        // 隐藏Infinite信息
        if (infiniteInfo) {
            infiniteInfo.style.display = 'none';
        }
    }
}

// 生成终点需求 - 确定性算法
function generateEndRequirements() {
    const requirements = [];
    
    // 定义可能的需求模式
    const demandPatterns = [
        // 简单模式：单一类型
        [{ type: 'square', color: '#ff5252', id: 'req1' }],
        
        // 双重模式：两种类型
        [
            { type: 'square', color: '#ff5252', id: 'req1' },
            { type: 'circle', color: '#2196F3', id: 'req2' }
        ],
        
        // 复杂模式：三种类型
        [
            { type: 'triangle', color: '#4CAF50', id: 'req1' },
            { type: 'diamond', color: '#FFC107', id: 'req2' },
            { type: 'square', color: '#ff5252', id: 'req3' }
        ]
    ];
    
    // 选择一个需求模式
    const selectedPattern = demandPatterns[Math.floor(Math.random() * demandPatterns.length)];
    return selectedPattern;
}

// 生成复杂的终点需求（基于难度）
function generateComplexEndRequirements(difficulty) {
    const requirements = [];
    const availableTypes = gameState.portTypes.slice(0, difficulty.types);
    const availableColors = gameState.portColors.slice(0, difficulty.colors);
    
    // 生成指定数量的转换链
    for (let i = 0; i < difficulty.chains; i++) {
        const type = availableTypes[Math.floor(Math.random() * availableTypes.length)];
        const color = availableColors[Math.floor(Math.random() * availableColors.length)];
        
        requirements.push({
            type: type,
            color: color,
            id: `req${i + 1}`
        });
    }
    
    return requirements;
}

// 生成匹配的起点提供（确定性版本）
function generateMatchingStartProvisions(endRequirements, difficulty) {
    const provisions = [];
    
    endRequirements.forEach((req, index) => {
        // 确定性地决定是否需要转换（基于关卡等级，不使用随机）
        const needsTransformation = difficulty.level > 2;
        
        if (needsTransformation) {
            // 确定性地选择转换类型（基于索引，不使用随机）
            if (index % 2 === 0) {
                // 偶数索引：改变类型，保持颜色
                const sourceType = getSourceType(req.type, difficulty.availableTypes);
                provisions.push({
                    type: sourceType,
                    color: req.color,
                    id: `prov${index + 1}`
                });
            } else {
                // 奇数索引：改变颜色，保持类型
                const sourceColor = getSourceColor(req.color, difficulty.availableColors);
                provisions.push({
                    type: req.type,
                    color: sourceColor,
                    id: `prov${index + 1}`
                });
            }
        } else {
            // 低难度：直接匹配
            provisions.push({
                type: req.type,
                color: req.color,
                id: `prov${index + 1}`
            });
        }
    });
    
    return provisions;
}

// 确定性地生成不同的类型
function generateDifferentType(targetType, difficulty) {
    const availableTypes = difficulty.availableTypes || gameState.portTypes.slice(0, difficulty.types);
    const differentTypes = availableTypes.filter(type => type !== targetType);
    
    if (differentTypes.length === 0) return targetType;
    
    // 确定性地选择第一个不同的类型
    return differentTypes[0];
}

// 确定性地生成不同的颜色
function generateDifferentColor(targetColor, difficulty) {
    const availableColors = difficulty.availableColors || gameState.portColors.slice(0, difficulty.colors);
    const differentColors = availableColors.filter(color => color !== targetColor);
    
    if (differentColors.length === 0) return targetColor;
    
    // 确定性地选择第一个不同的颜色
    return differentColors[0];
}

// 生成高级转换链
function generateAdvancedTransformationChains(startProvisions, endRequirements, difficulty) {
    const chains = [];
    
    for (let i = 0; i < startProvisions.length; i++) {
        const source = startProvisions[i];
        const target = endRequirements[i];
        
        // 生成转换链
        const chain = {
            id: `chain${i + 1}`,
            source: source,
            target: target,
            steps: []
        };
        
        // 如果源和目标不同，生成转换步骤
        if (source.type !== target.type || source.color !== target.color) {
            const steps = generateTransformationSteps(source, target, difficulty);
            chain.steps = steps;
        }
        
        chains.push(chain);
    }
    
    return chains;
}

// 生成转换步骤
function generateTransformationSteps(source, target, difficulty) {
    const steps = [];
    let currentType = source.type;
    let currentColor = source.color;
    let stepId = 1;
    
    // 先转换类型，再转换颜色
    if (currentType !== target.type) {
        steps.push({
            id: `step${stepId++}`,
            inputType: currentType,
            inputColor: currentColor,
            outputType: target.type,
            outputColor: currentColor
        });
        currentType = target.type;
    }
    
    if (currentColor !== target.color) {
        steps.push({
            id: `step${stepId++}`,
            inputType: currentType,
            inputColor: currentColor,
            outputType: currentType,
            outputColor: target.color
        });
        currentColor = target.color;
    }
    
    return steps;
}

// 创建高级转换节点（支持多端口和多解）
function createAdvancedTransformationNodes(transformationChains, difficulty) {
    const nodes = [];
    const nodeTypeCounter = new Map();
    const solutionPaths = []; // 跟踪所有可能的解决方案路径
    
    transformationChains.forEach(chain => {
        chain.steps.forEach(step => {
            // 创建转换节点
            const node = new Node('normal');
            
            // 设置节点的转换功能
            node.stepInfo = {
                id: step.id,
                inputType: step.inputType,
                inputColor: step.inputColor,
                outputType: step.outputType,
                outputColor: step.outputColor
            };
            
            // 创建多个输入端口（支持复合转换）
            const numInputs = Math.min(difficulty.maxPortsPerNode || 3, Math.max(1, Math.floor(Math.random() * 3) + 1));
            for (let i = 0; i < numInputs; i++) {
                if (i === 0) {
                    // 主要输入端口
                    node.addInputPort(step.inputType, step.inputColor);
                } else {
                    // 额外的输入端口（用于创建多解）
                    const extraType = difficulty.availableTypes[Math.floor(Math.random() * difficulty.availableTypes.length)];
                    const extraColor = difficulty.availableColors[Math.floor(Math.random() * difficulty.availableColors.length)];
                    node.addInputPort(extraType, extraColor);
                }
            }
            
            // 创建多个输出端口（支持分支和多解）
            const numOutputs = Math.min(difficulty.maxPortsPerNode || 3, Math.max(1, Math.floor(Math.random() * 3) + 1));
            for (let i = 0; i < numOutputs; i++) {
                if (i === 0) {
                    // 主要输出端口
                    node.addOutputPort(step.outputType, step.outputColor);
                } else {
                    // 额外的输出端口（可能是转换变体）
                    const useVariation = Math.random() < 0.6;
                    if (useVariation) {
                        // 创建相同类型但不同颜色的变体
                        const variantColor = difficulty.availableColors[Math.floor(Math.random() * difficulty.availableColors.length)];
                        node.addOutputPort(step.outputType, variantColor);
                    } else {
                        // 创建完全不同的输出
                        const extraType = difficulty.availableTypes[Math.floor(Math.random() * difficulty.availableTypes.length)];
                        const extraColor = difficulty.availableColors[Math.floor(Math.random() * difficulty.availableColors.length)];
                        node.addOutputPort(extraType, extraColor);
                    }
                }
            }
            
            // 更新节点标签以反映多端口
            const inputLabels = node.inputPorts.map(p => `${p.type}:${p.color.substr(1,2)}`).join(',');
            const outputLabels = node.outputPorts.map(p => `${p.type}:${p.color.substr(1,2)}`).join(',');
            node.label = `[${inputLabels}]→[${outputLabels}]`;
            
            nodes.push(node);
            
            // 创建备选节点（提供不同的解决方案路径）
            if (difficulty.duplicateNodes && Math.random() < 0.5) {
                const alternativeNode = createAlternativeNode(step, difficulty, nodeTypeCounter);
                if (alternativeNode) {
                    nodes.push(alternativeNode);
                    solutionPaths.push({
                        original: node.id,
                        alternative: alternativeNode.id,
                        transformation: step
                    });
                }
            }
        });
    });
    
    // 为高难度关卡创建额外的"桥接"节点，增加解的多样性
    if (difficulty.level > 10) {
        const bridgeNodes = createBridgeNodesForMultipleSolutions(nodes, difficulty);
        nodes.push(...bridgeNodes);
    }
    
    return nodes;
}

// 创建备选节点（提供不同的解决方案）
function createAlternativeNode(baseStep, difficulty, nodeTypeCounter) {
    const node = new Node('normal');
    
    // 创建与原始节点功能相似但路径不同的备选节点
    const useIndirectPath = Math.random() < 0.7;
    
    if (useIndirectPath) {
        // 创建需要多步转换的间接路径
        const intermediateType = difficulty.availableTypes[Math.floor(Math.random() * difficulty.availableTypes.length)];
        const intermediateColor = difficulty.availableColors[Math.floor(Math.random() * difficulty.availableColors.length)];
        
        // 输入：与原始相同
        node.addInputPort(baseStep.inputType, baseStep.inputColor);
        // 输出：中间结果（需要另一个节点完成最终转换）
        node.addOutputPort(intermediateType, intermediateColor);
        
        node.label = `Alt:${baseStep.inputType}→${intermediateType}`;
    } else {
        // 创建功能等效但端口组合不同的节点
        node.addInputPort(baseStep.inputType, baseStep.inputColor);
        
        // 可能需要额外输入
        if (Math.random() < 0.4) {
            const extraType = difficulty.availableTypes[Math.floor(Math.random() * difficulty.availableTypes.length)];
            const extraColor = difficulty.availableColors[Math.floor(Math.random() * difficulty.availableColors.length)];
            node.addInputPort(extraType, extraColor);
        }
        
        node.addOutputPort(baseStep.outputType, baseStep.outputColor);
        
        node.label = `Alt:${node.inputPorts.map(p => p.type).join('+')}→${baseStep.outputType}`;
    }
    
    return node;
}

// 创建桥接节点以增加解的多样性
function createBridgeNodesForMultipleSolutions(existingNodes, difficulty) {
    const bridgeNodes = [];
    const maxBridges = Math.min(5, Math.floor(difficulty.level / 3));
    
    for (let i = 0; i < maxBridges; i++) {
        const bridge = new Node('normal');
        
        // 分析现有节点的输出和输入，找到可以连接的类型
        const availableOutputs = [];
        const availableInputs = [];
        
        existingNodes.forEach(node => {
            node.outputPorts.forEach(port => {
                availableOutputs.push({ type: port.type, color: port.color });
            });
            node.inputPorts.forEach(port => {
                availableInputs.push({ type: port.type, color: port.color });
            });
        });
        
        if (availableOutputs.length > 0 && availableInputs.length > 0) {
            // 选择一个可用输出作为输入
            const chosenInput = availableOutputs[Math.floor(Math.random() * availableOutputs.length)];
            bridge.addInputPort(chosenInput.type, chosenInput.color);
            
            // 选择一个需要的输入作为输出（或创建新的）
            const useExistingNeed = Math.random() < 0.7;
            if (useExistingNeed && availableInputs.length > 0) {
                const chosenOutput = availableInputs[Math.floor(Math.random() * availableInputs.length)];
                bridge.addOutputPort(chosenOutput.type, chosenOutput.color);
            } else {
                // 创建新的输出类型
                const newType = difficulty.availableTypes[Math.floor(Math.random() * difficulty.availableTypes.length)];
                const newColor = difficulty.availableColors[Math.floor(Math.random() * difficulty.availableColors.length)];
                bridge.addOutputPort(newType, newColor);
            }
            
            bridge.label = `Bridge${i + 1}`;
            bridgeNodes.push(bridge);
        }
    }
    
    return bridgeNodes;
}

// 生成起点提供 - 确保与终点需求不直接匹配，考虑一对一约束
function generateStartProvisions(endRequirements) {
    const provisions = [];
    
    // 定义基础类型池
    const baseTypes = [
        { type: 'square', color: '#ff9999', id: 'base1' },  // 浅红色，需要转换为深红
        { type: 'circle', color: '#99ccff', id: 'base2' },  // 浅蓝色，需要转换为深蓝
        { type: 'triangle', color: '#99ff99', id: 'base3' }, // 浅绿色，需要转换为深绿
        { type: 'diamond', color: '#ffff99', id: 'base4' }   // 浅黄色，需要转换为深黄
    ];
    
    // 确保起点输出数量 >= 终点输入数量（一对一约束）
    const numProvisions = endRequirements ? Math.max(endRequirements.length, 1) : Math.floor(Math.random() * 2) + 1;
    
    // 选择不重复的基础类型
    const selectedTypes = [];
    for (let i = 0; i < Math.min(numProvisions, baseTypes.length); i++) {
        selectedTypes.push(baseTypes[i]);
    }
    
    // 如果需要的数量超过可用类型，复制一些类型（但改变ID）
    while (selectedTypes.length < numProvisions) {
        const template = baseTypes[selectedTypes.length % baseTypes.length];
        selectedTypes.push({
            ...template,
            id: `${template.id}_copy_${selectedTypes.length}`
        });
    }
    
    return selectedTypes;
}

// 设计最小转换链 - 考虑端口一对一约束
function designMinimalTransformationChain(startProvisions, endRequirements) {
    const transformationChain = [];
    const usedSources = new Set(); // 跟踪已使用的源端口
    
    // 第一步：检查是否需要分支节点
    const { needsBranching, branchingPlan } = analyzeBranchingNeeds(startProvisions, endRequirements);
    
    if (needsBranching) {
        // 生成带分支的转换链
        return generateBranchingTransformationChain(startProvisions, endRequirements, branchingPlan);
    }
    
    // 第二步：为每个终点需求分配独立的源
    const sourceAssignments = assignSourcesToTargets(startProvisions, endRequirements);
    
    // 第三步：为每个分配设计转换路径
    sourceAssignments.forEach((assignment, index) => {
        const steps = designTransformationSteps(assignment.source, assignment.target);
        transformationChain.push({
            source: assignment.source,
            target: assignment.target,
            steps: steps,
            id: `chain_${index + 1}`,
            dedicated: true // 标记为独立链
        });
    });
    
    return transformationChain;
}

// 分析是否需要分支节点
function analyzeBranchingNeeds(startProvisions, endRequirements) {
    const sourceTypeCount = {};
    const targetTypeCount = {};
    
    // 统计源类型
    startProvisions.forEach(source => {
        const key = `${source.type}_${source.color}`;
        sourceTypeCount[key] = (sourceTypeCount[key] || 0) + 1;
    });
    
    // 统计目标类型需求
    endRequirements.forEach(target => {
        const key = `${target.type}_${target.color}`;
        targetTypeCount[key] = (targetTypeCount[key] || 0) + 1;
    });
    
    // 检查是否有目标需求超过源提供
    const branchingNeeds = [];
    for (const [typeKey, targetCount] of Object.entries(targetTypeCount)) {
        const sourceCount = sourceTypeCount[typeKey] || 0;
        if (targetCount > sourceCount) {
            branchingNeeds.push({
                type: typeKey,
                sourceCount,
                targetCount,
                needsBranches: targetCount - sourceCount
            });
        }
    }
    
    return {
        needsBranching: branchingNeeds.length > 0,
        branchingPlan: branchingNeeds
    };
}

// 分配源到目标
function assignSourcesToTargets(startProvisions, endRequirements) {
    const assignments = [];
    const availableSources = [...startProvisions];
    
    endRequirements.forEach(requirement => {
        // 寻找直接匹配的源
        let sourceIndex = availableSources.findIndex(source =>
            source.type === requirement.type && source.color === requirement.color
        );
        
        if (sourceIndex === -1) {
            // 寻找可以转换的源
            sourceIndex = availableSources.findIndex(source => 
                canTransformTo(source, requirement)
            );
        }
        
        if (sourceIndex === -1) {
            // 使用第一个可用源（需要多步转换）
            sourceIndex = 0;
        }
        
        if (sourceIndex >= 0 && sourceIndex < availableSources.length) {
            assignments.push({
                source: availableSources[sourceIndex],
                target: requirement
            });
            availableSources.splice(sourceIndex, 1); // 移除已使用的源
        }
    });
    
    return assignments;
}

// 检查是否可以转换
function canTransformTo(source, target) {
    // 单步转换：只改变类型或颜色，不能同时改变两者
    const sameType = source.type === target.type;
    const sameColor = source.color === target.color;
    return sameType || sameColor;
}

// 生成带分支的转换链
function generateBranchingTransformationChain(startProvisions, endRequirements, branchingPlan) {
    const transformationChain = [];
    
    // 简化方案：重新设计起点和终点以避免分支
    // 在真实场景中，我们应该生成分支节点，但为了简化先调整需求
    const adjustedRequirements = adjustRequirementsToAvoidBranching(startProvisions, endRequirements);
    
    // 避免递归调用，直接创建简单的转换链
    const sourceAssignments = assignSourcesToTargets(startProvisions, adjustedRequirements);
    
    // 为每个分配设计转换路径
    sourceAssignments.forEach((assignment, index) => {
        const steps = designTransformationSteps(assignment.source, assignment.target);
        transformationChain.push({
            source: assignment.source,
            target: assignment.target,
            steps: steps,
            id: `chain_${index + 1}`,
            dedicated: true // 标记为独立链
        });
    });
    
    return transformationChain;
}

// 调整需求以避免分支
function adjustRequirementsToAvoidBranching(startProvisions, endRequirements) {
    const adjusted = [];
    const sourceTypes = startProvisions.map(s => `${s.type}_${s.color}`);
    
    endRequirements.forEach((req, index) => {
        if (index < sourceTypes.length) {
            adjusted.push(req);
        }
        // 超出的需求将被忽略，确保一对一映射
    });
    
    return adjusted;
}

// 设计具体的转换步骤
function designTransformationSteps(source, target) {
    const steps = [];
    
    // 如果类型不同，需要类型转换节点
    if (source.type !== target.type) {
        steps.push({
            type: 'type_converter',
            inputType: source.type,
            inputColor: source.color,
            outputType: target.type,
            outputColor: source.color, // 先转换类型，保持颜色
            id: `type_conv_${source.type}_to_${target.type}`
        });
        
        // 更新当前状态
        source = { 
            type: target.type, 
            color: source.color, 
            id: `intermediate_${steps.length}` 
        };
    }
    
    // 如果颜色不同，需要颜色转换节点
    if (source.color !== target.color) {
        steps.push({
            type: 'color_converter',
            inputType: source.type,
            inputColor: source.color,
            outputType: source.type,
            outputColor: target.color,
            id: `color_conv_${source.color}_to_${target.color}`
        });
    }
    
    return steps;
}

// 创建起点节点（支持多个起点）
function createStartNode(provisions) {
    const startNodes = [];
    const groupedProvisions = groupProvisionsByType(provisions);
    
    // 如果有多个不同类型的提供，或者单个类型数量过多，创建多个起点
    if (groupedProvisions.length > 2 || provisions.length > 4) {
        // 创建多个起点节点
        groupedProvisions.forEach((group, index) => {
            const startNode = new Node(`start_${index + 1}`);
            startNode.type = 'start';
            startNode.label = `起点 ${index + 1}`;
            
            group.forEach(provision => {
                startNode.addOutputPort(provision.type, provision.color);
            });
            
            startNodes.push(startNode);
        });
    } else {
        // 创建单个起点节点
        const startNode = new Node('start');
        provisions.forEach(provision => {
            startNode.addOutputPort(provision.type, provision.color);
        });
        startNodes.push(startNode);
    }
    
    return startNodes.length === 1 ? startNodes[0] : startNodes;
}

// 创建终点节点（支持多个终点）
function createEndNode(requirements) {
    const endNodes = [];
    const groupedRequirements = groupRequirementsByType(requirements);
    
    // 如果有多个不同类型的需求，或者单个类型数量过多，创建多个终点
    if (groupedRequirements.length > 2 || requirements.length > 4) {
        // 创建多个终点节点
        groupedRequirements.forEach((group, index) => {
            const endNode = new Node(`end_${index + 1}`);
            endNode.type = 'end';
            endNode.label = `终点 ${index + 1}`;
            
            group.forEach(requirement => {
                endNode.addInputPort(requirement.type, requirement.color);
            });
            
            endNodes.push(endNode);
        });
    } else {
        // 创建单个终点节点
        const endNode = new Node('end');
        requirements.forEach(requirement => {
            endNode.addInputPort(requirement.type, requirement.color);
        });
        endNodes.push(endNode);
    }
    
    return endNodes.length === 1 ? endNodes[0] : endNodes;
}

// 按类型分组提供
function groupProvisionsByType(provisions) {
    const groups = [];
    const typeMap = new Map();
    
    provisions.forEach(provision => {
        const key = `${provision.type}_${provision.color}`;
        if (!typeMap.has(key)) {
            typeMap.set(key, []);
            groups.push(typeMap.get(key));
        }
        typeMap.get(key).push(provision);
    });
    
    return groups;
}

// 按类型分组需求
function groupRequirementsByType(requirements) {
    const groups = [];
    const typeMap = new Map();
    
    requirements.forEach(requirement => {
        const key = `${requirement.type}_${requirement.color}`;
        if (!typeMap.has(key)) {
            typeMap.set(key, []);
            groups.push(typeMap.get(key));
        }
        typeMap.get(key).push(requirement);
    });
    
    return groups;
}

// 创建转换节点
function createTransformationNodes(transformationChain) {
    const nodes = [];
    
    transformationChain.forEach(chain => {
        chain.steps.forEach((step, stepIndex) => {
            const node = new Node('normal');
            node.stepInfo = step; // 保存步骤信息用于调试
            
            // 添加输入端口
            node.addInputPort(step.inputType, step.inputColor);
            
            // 添加输出端口
            node.addOutputPort(step.outputType, step.outputColor);
            
            nodes.push(node);
        });
    });
    
    return nodes;
}

// 验证严格可解性
function verifyStrictSolvability(nodeSet) {
    const verification = {
        isSolvable: false,
        errors: [],
        warnings: [],
        solution: null
    };
    
    // 创建所有可用节点的完整列表
    const allNodes = [nodeSet.startNode, nodeSet.endNode, ...nodeSet.requiredNodes];
    
    // 验证一对一连接约束
    const oneToOneCheck = verifyOneToOneConstraint(nodeSet);
    if (!oneToOneCheck.isValid) {
        verification.errors.push(`一对一连接约束验证失败: ${oneToOneCheck.error}`);
    }
    
    // 验证端口数量平衡
    const portBalanceCheck = verifyPortBalance(nodeSet);
    if (!portBalanceCheck.isBalanced) {
        verification.errors.push(`端口数量不平衡: ${portBalanceCheck.error}`);
    }
    
    // 验证每个转换链是否可以实现
    for (const chain of nodeSet.solution) {
        const chainVerification = verifyTransformationChain(chain, allNodes);
        if (!chainVerification.isValid) {
            verification.errors.push(`转换链 ${chain.id} 验证失败: ${chainVerification.error}`);
        }
    }
    
    // 验证节点完备性（每个节点都是必需的）
    const completenessCheck = verifyNodeCompleteness(nodeSet);
    if (!completenessCheck.isComplete) {
        verification.errors.push(`节点完备性检查失败: ${completenessCheck.error}`);
    }
    
    // 验证解的唯一性
    const uniquenessCheck = verifyUniqueness(nodeSet);
    if (!uniquenessCheck.isUnique) {
        verification.warnings.push(`解可能不唯一: ${uniquenessCheck.warning}`);
    }
    
    verification.isSolvable = verification.errors.length === 0;
    verification.solution = nodeSet.solution;
    
    return verification;
}

// 验证一对一连接约束
function verifyOneToOneConstraint(nodeSet) {
    const allNodes = [nodeSet.startNode, nodeSet.endNode, ...nodeSet.requiredNodes];
    
    // 模拟连接以检查约束
    const portUsage = new Map(); // 跟踪每个端口的使用情况
    
    for (const chain of nodeSet.solution) {
        // 检查每个转换链的端口使用
        let currentNode = nodeSet.startNode;
        
        // 查找起点对应的输出端口
        const startOutputPort = currentNode.outputPorts.find(port =>
            port.type === chain.source.type && port.color === chain.source.color
        );
        
        if (!startOutputPort) {
            return {
                isValid: false,
                error: `起点缺少必需的输出端口: ${chain.source.type}(${chain.source.color})`
            };
        }
        
        // 记录端口使用
        const startPortKey = `${currentNode.id}_${startOutputPort.type}_${startOutputPort.color}_output`;
        if (portUsage.has(startPortKey)) {
            return {
                isValid: false,
                error: `端口重复使用: 起点输出端口 ${chain.source.type}(${chain.source.color})`
            };
        }
        portUsage.set(startPortKey, chain.id);
        
        // 检查转换步骤中的端口使用
        for (let i = 0; i < chain.steps.length; i++) {
            const step = chain.steps[i];
            
            // 查找对应的转换节点
            const transformNode = nodeSet.requiredNodes.find(node =>
                node.stepInfo && node.stepInfo.id === step.id
            );
            
            if (!transformNode) {
                return {
                    isValid: false,
                    error: `缺少转换节点: ${step.id}`
                };
            }
            
            // 检查输入端口使用
            const inputPortKey = `${transformNode.id}_${step.inputType}_${step.inputColor}_input`;
            if (portUsage.has(inputPortKey)) {
                return {
                    isValid: false,
                    error: `端口重复使用: 节点输入端口 ${step.inputType}(${step.inputColor})`
                };
            }
            portUsage.set(inputPortKey, chain.id);
            
            // 检查输出端口使用
            const outputPortKey = `${transformNode.id}_${step.outputType}_${step.outputColor}_output`;
            if (portUsage.has(outputPortKey)) {
                return {
                    isValid: false,
                    error: `端口重复使用: 节点输出端口 ${step.outputType}(${step.outputColor})`
                };
            }
            portUsage.set(outputPortKey, chain.id);
        }
        
        // 检查终点输入端口使用
        const endInputPort = nodeSet.endNode.inputPorts.find(port =>
            port.type === chain.target.type && port.color === chain.target.color
        );
        
        if (!endInputPort) {
            return {
                isValid: false,
                error: `终点缺少必需的输入端口: ${chain.target.type}(${chain.target.color})`
            };
        }
        
        const endPortKey = `${nodeSet.endNode.id}_${endInputPort.type}_${endInputPort.color}_input`;
        if (portUsage.has(endPortKey)) {
            return {
                isValid: false,
                error: `端口重复使用: 终点输入端口 ${chain.target.type}(${chain.target.color})`
            };
        }
        portUsage.set(endPortKey, chain.id);
    }
    
    return { isValid: true };
}

// 验证端口数量平衡
function verifyPortBalance(nodeSet) {
    const allNodes = [nodeSet.startNode, nodeSet.endNode, ...nodeSet.requiredNodes];
    
    // 统计总的输入和输出端口数量
    let totalInputPorts = 0;
    let totalOutputPorts = 0;
    
    allNodes.forEach(node => {
        totalInputPorts += node.inputPorts.length;
        totalOutputPorts += node.outputPorts.length;
    });
    
    // 起点没有输入，终点没有输出，中间节点需要平衡
    const expectedConnections = nodeSet.solution.length;
    const availableConnections = Math.min(totalInputPorts, totalOutputPorts);
    
    if (availableConnections < expectedConnections) {
        return {
            isBalanced: false,
            error: `端口数量不足: 需要 ${expectedConnections} 个连接，但只有 ${availableConnections} 个可用连接`
        };
    }
    
    return { isBalanced: true };
}

// 验证转换链
function verifyTransformationChain(chain, allNodes) {
    // 检查是否存在必要的转换节点
    for (const step of chain.steps) {
        const matchingNode = allNodes.find(node => 
            node.stepInfo && 
            node.stepInfo.id === step.id
        );
        
        if (!matchingNode) {
            return {
                isValid: false,
                error: `缺少转换节点: ${step.id}`
            };
        }
    }
    
    return { isValid: true };
}

// 验证节点完备性
function verifyNodeCompleteness(nodeSet) {
    // 检查是否所有必需节点都存在
    const requiredSteps = nodeSet.solution.flatMap(chain => chain.steps);
    const availableNodes = nodeSet.requiredNodes;
    
    if (requiredSteps.length !== availableNodes.length) {
        return {
            isComplete: false,
            error: `节点数量不匹配: 需要 ${requiredSteps.length}, 提供 ${availableNodes.length}`
        };
    }
    
    return { isComplete: true };
}

// 验证解的唯一性
function verifyUniqueness(nodeSet) {
    // 简单检查：确保没有冗余节点
    const stepIds = nodeSet.solution.flatMap(chain => chain.steps.map(step => step.id));
    const uniqueStepIds = [...new Set(stepIds)];
    
    if (stepIds.length !== uniqueStepIds.length) {
        return {
            isUnique: false,
            warning: '存在重复的转换步骤'
        };
    }
    
    return { isUnique: true };
}

// 生成兼容的起点和终点
function generateCompatibleStartEndNodes() {
    const startNode = new Node('start');
    const endNode = new Node('end');
    
    // 定义一些常见的端口类型组合
    const portCombos = [
        [{ type: 'square', color: '#ff5252' }],
        [{ type: 'circle', color: '#2196F3' }],
        [{ type: 'triangle', color: '#4CAF50' }],
        [{ type: 'square', color: '#ff5252' }, { type: 'circle', color: '#2196F3' }],
        [{ type: 'triangle', color: '#4CAF50' }, { type: 'diamond', color: '#FFC107' }]
    ];
    
    // 随机选择一个组合
    const selectedCombo = portCombos[Math.floor(Math.random() * portCombos.length)];
    
    // 为起点添加输出端口
    selectedCombo.forEach(portSpec => {
        startNode.addOutputPort(portSpec.type, portSpec.color);
    });
    
    // 为终点添加对应的输入端口
    selectedCombo.forEach(portSpec => {
        endNode.addInputPort(portSpec.type, portSpec.color);
    });
    
    // 可选：为终点添加一些额外的输入端口增加挑战性
    if (Math.random() < 0.3) {
        const extraPortSpec = portCombos[0][0]; // 使用基础类型
        endNode.addInputPort(extraPortSpec.type, extraPortSpec.color);
    }
    
    return { startNode, endNode };
}

// 生成桥接节点（保证可以连接起点到终点）
function generateBridgeNodes(startNode, endNode, count) {
    const bridgeNodes = [];
    
    for (let i = 0; i < count; i++) {
        const node = new Node('normal');
        
        // 能够接收起点的输出
        const startOutputTypes = startNode.outputPorts.map(port => ({ type: port.type, color: port.color }));
        const randomStartType = startOutputTypes[Math.floor(Math.random() * startOutputTypes.length)];
        node.addInputPort(randomStartType.type, randomStartType.color);
        
        // 能够向终点提供输入
        const endInputTypes = endNode.inputPorts.map(port => ({ type: port.type, color: port.color }));
        const randomEndType = endInputTypes[Math.floor(Math.random() * endInputTypes.length)];
        node.addOutputPort(randomEndType.type, randomEndType.color);
        
        // 可选：添加一些额外的端口
        if (Math.random() < 0.4) {
            // 添加额外输入
            const extraInputType = startOutputTypes[Math.floor(Math.random() * startOutputTypes.length)];
            node.addInputPort(extraInputType.type, extraInputType.color);
        }
        
        if (Math.random() < 0.4) {
            // 添加额外输出
            const extraOutputType = endInputTypes[Math.floor(Math.random() * endInputTypes.length)];
            node.addOutputPort(extraOutputType.type, extraOutputType.color);
        }
        
        bridgeNodes.push(node);
    }
    
    return bridgeNodes;
}

// 生成智能随机节点
function generateSmartRandomNodes(count) {
    const smartNodes = [];
    
    for (let i = 0; i < count; i++) {
        // 使用已有的智能生成函数
        const node = generateDefaultRandomNode();
        smartNodes.push(node);
    }
    
    return smartNodes;
}

// 验证场景的可解性
function verifyScenarioSolvability() {
    // 创建一个临时的完整节点列表进行分析
    const allNodes = [...gameState.placedNodes, ...gameState.temporaryNodes];
    
    // 获取起点和终点
    const startNodes = allNodes.filter(node => node.type === 'start');
    const endNodes = allNodes.filter(node => node.type === 'end');
    
    if (startNodes.length === 0 || endNodes.length === 0) return false;
    
    // 对每个起点-终点对检查可解性
    for (const startNode of startNodes) {
        for (const endNode of endNodes) {
            if (canSolveConnection(startNode, endNode, allNodes)) {
                return true; // 至少有一个可解的路径
            }
        }
    }
    
    return false;
}

// 检查是否可以通过可用节点连接起点到终点
function canSolveConnection(startNode, endNode, availableNodes) {
    // 使用贪心算法模拟最优连接
    const usedNodes = new Set([startNode.id]);
    let currentOutputs = startNode.outputPorts.map(port => ({ type: port.type, color: port.color, nodeId: startNode.id }));
    
    const targetInputs = endNode.inputPorts.map(port => ({ type: port.type, color: port.color }));
    const remainingTargets = [...targetInputs];
    
    // 尝试通过中间节点建立连接
    while (remainingTargets.length > 0 && currentOutputs.length > 0) {
        let foundConnection = false;
        
        // 尝试直接连接到终点
        for (let i = remainingTargets.length - 1; i >= 0; i--) {
            const target = remainingTargets[i];
            const matchingOutput = currentOutputs.find(output => 
                output.type === target.type && output.color === target.color
            );
            
            if (matchingOutput) {
                remainingTargets.splice(i, 1);
                foundConnection = true;
            }
        }
        
        if (remainingTargets.length === 0) break;
        
        // 尝试通过中间节点
        for (const node of availableNodes) {
            if (usedNodes.has(node.id) || node.type !== 'normal') continue;
            
            // 检查是否可以连接到这个节点
            const canConnectToNode = node.inputPorts.some(inputPort =>
                currentOutputs.some(output => 
                    output.type === inputPort.type && output.color === inputPort.color
                )
            );
            
            if (canConnectToNode) {
                // 添加这个节点的输出到可用输出
                node.outputPorts.forEach(outputPort => {
                    currentOutputs.push({ 
                        type: outputPort.type, 
                        color: outputPort.color, 
                        nodeId: node.id 
                    });
                });
                
                usedNodes.add(node.id);
                foundConnection = true;
                break;
            }
        }
        
        if (!foundConnection) break; // 无法继续连接
    }
    
    return remainingTargets.length === 0; // 所有目标都被满足
}

// 分析当前蓝图并提供建议
function analyzeBlueprintAndSuggestNodes() {
    const analysis = {
        missingConnections: [],
        suggestedNodeTypes: [],
        canSolve: false
    };
    
    const startNodes = gameState.placedNodes.filter(node => node.type === 'start');
    const endNodes = gameState.placedNodes.filter(node => node.type === 'end');
    
    if (startNodes.length === 0 || endNodes.length === 0) {
        return analysis;
    }
    
    // 分析每个起点到终点的连接需求
    for (const startNode of startNodes) {
        for (const endNode of endNodes) {
            const pathAnalysis = analyzeConnectionPath(startNode, endNode);
            analysis.missingConnections.push(...pathAnalysis.missing);
            analysis.suggestedNodeTypes.push(...pathAnalysis.suggestions);
        }
    }
    
    analysis.canSolve = verifyScenarioSolvability();
    return analysis;
}

// 分析特定连接路径
function analyzeConnectionPath(startNode, endNode) {
    const missing = [];
    const suggestions = [];
    
    const availableOutputs = [];
    const placedNormalNodes = gameState.placedNodes.filter(node => node.type === 'normal');
    
    // 收集所有可用的输出
    [...[startNode], ...placedNormalNodes].forEach(node => {
        node.outputPorts.forEach(port => {
            const isConnected = gameState.connections.some(conn => conn.fromPort.id === port.id);
            if (!isConnected) {
                availableOutputs.push({ type: port.type, color: port.color, nodeId: node.id });
            }
        });
    });
    
    // 检查终点的输入需求
    endNode.inputPorts.forEach(port => {
        const isConnected = gameState.connections.some(conn => conn.toPort.id === port.id);
        if (!isConnected) {
            const hasMatchingOutput = availableOutputs.some(output => 
                output.type === port.type && output.color === port.color
            );
            
            if (!hasMatchingOutput) {
                missing.push({ type: port.type, color: port.color, for: 'end' });
                suggestions.push({
                    description: `需要一个能输出 ${port.type}(${port.color}) 的节点`,
                    requiredOutputs: [{ type: port.type, color: port.color }],
                    requiredInputs: availableOutputs // 可以接受任何现有输出
                });
            }
        }
    });
    
    return { missing, suggestions };
}

// 设置事件监听器
function setupEventListeners() {
    // 初始化两个canvas
    const tempCanvas = document.getElementById('temp-canvas');
    const gameCanvas = document.getElementById('game-canvas');
    
    if (tempCanvas) {
        gameState.temporaryArea.canvas = tempCanvas;
        gameState.temporaryArea.ctx = tempCanvas.getContext('2d');
        
        // 临时区域事件
        tempCanvas.addEventListener('mousedown', onMouseDown);
        tempCanvas.addEventListener('mousemove', onMouseMove);
        tempCanvas.addEventListener('mouseup', onMouseUp);
        tempCanvas.addEventListener('mouseleave', onMouseLeave);
        tempCanvas.addEventListener('contextmenu', e => e.preventDefault());
        tempCanvas.addEventListener('selectstart', e => e.preventDefault());
    }
    
    if (gameCanvas) {
        gameState.placementArea.canvas = gameCanvas;
        gameState.placementArea.ctx = gameCanvas.getContext('2d');
        
        // 摆放区域事件
        gameCanvas.addEventListener('mousedown', onMouseDown);
        gameCanvas.addEventListener('mousemove', onMouseMove);
        gameCanvas.addEventListener('mouseup', onMouseUp);
        gameCanvas.addEventListener('mouseleave', onMouseLeave);
        gameCanvas.addEventListener('contextmenu', e => e.preventDefault());
        gameCanvas.addEventListener('selectstart', e => e.preventDefault());
    }
    
    // 键盘事件
    document.addEventListener('keydown', onKeyDown);
    
    // 按钮事件
    document.getElementById('validate-btn').addEventListener('click', () => {
        validateBlueprint();
    });
    
    document.getElementById('play-flow-btn').addEventListener('click', () => {
        playFlowAnimation();
    });
    
    document.getElementById('clear-btn').addEventListener('click', () => {
        clearBlueprint();
    });
    
    document.getElementById('regenerate-btn').addEventListener('click', () => {
        if (gameState.mode === 'puzzle') {
            generatePuzzleLevel();
        } else if (gameState.mode === 'tetris') {
            startTetrisLevel();
        } else if (gameState.mode === 'infinite') {
            startInfiniteLevel();
        } else {
            generateSolvableScenario();
        }
        updateMessage('已重新生成关卡！');
    });
    
    // 游戏模式选择按钮
    document.getElementById('puzzle-mode-btn').addEventListener('click', () => switchMode('puzzle'));
    document.getElementById('tetris-mode-btn').addEventListener('click', () => switchMode('tetris'));
    document.getElementById('infinite-mode-btn').addEventListener('click', () => switchMode('infinite'));
    
    document.getElementById('hint-btn').addEventListener('click', () => {
        toggleSolutionHints();
        gameState.levelConfig.levelProgress.hintsUsed++;
    });
    
    // 线路交叉检测按钮事件
    document.getElementById('crossing-toggle-btn').addEventListener('click', () => {
        toggleCrossingRestriction();
        updateCrossingButtonStates();
    });
    
    document.getElementById('crosspoint-toggle-btn').addEventListener('click', () => {
        toggleCrossPointDisplay();
        updateCrossingButtonStates();
    });
    
    // 多节点模式控制按钮事件
    document.getElementById('multi-start-toggle-btn').addEventListener('click', () => {
        toggleMultipleStartNodes();
        updateMultiNodeButtonStates();
    });
    
    document.getElementById('multi-end-toggle-btn').addEventListener('click', () => {
        toggleMultipleEndNodes();
        updateMultiNodeButtonStates();
    });
}

// 鼠标按下事件
function onMouseDown(event) {
    const rect = event.target.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;
    
    // 确定是在哪个canvas上操作
    const isInTempArea = event.target.id === 'temp-canvas';
    const isInGameArea = event.target.id === 'game-canvas';
    
    // 检查是否点击了端口
    const port = findPortAtPosition(x, y, isInTempArea ? 'temp' : 'game');
    if (port) {
        startConnectionDrag(port, x, y);
        return;
    }
    
    // 检查是否点击了节点
    const node = findNodeAtPosition(x, y, isInTempArea ? 'temp' : 'game');
    if (node) {
        startNodeDrag(node, x, y, isInTempArea ? 'temp' : 'game');
        return;
    }
}

// 鼠标移动事件
function onMouseMove(event) {
    const rect = event.target.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;
    
    // 确定当前鼠标在哪个区域
    const currentArea = event.target.id === 'temp-canvas' ? 'temp' : 'game';
    
    if (gameState.draggingNode) {
        updateNodeDrag(x, y, currentArea);
    } else if (gameState.draggingConnection) {
        updateConnectionDrag(x, y);
    }
    
    render();
}

// 鼠标松开事件
function onMouseUp(event) {
    const rect = event.target.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;
    
    // 确定当前鼠标在哪个区域
    const currentArea = event.target.id === 'temp-canvas' ? 'temp' : 'game';
    
    if (gameState.draggingNode) {
        finishNodeDrag(x, y, currentArea);
    } else if (gameState.draggingConnection) {
        finishConnectionDrag(x, y);
    }
    
    render();
}

// 鼠标离开事件
function onMouseLeave(event) {
    // 如果鼠标离开canvas时正在拖拽，保持拖拽状态但清除预览
    if (gameState.draggingNode && gameState.dragPreview) {
        clearDragPreview();
        render();
    }
}

// 键盘事件
function onKeyDown(event) {
    switch(event.key) {
        case 'Delete':
        case 'Backspace':
            if (gameState.selectedNode) {
                deleteNode(gameState.selectedNode);
            }
            break;
        case 'Escape':
            gameState.selectedNode = null;
            break;
        case ' ': // 空格键 - 播放流动动画
            event.preventDefault();
            playFlowAnimation();
            break;
        case 'Enter': // 回车键 - 验证蓝图
            event.preventDefault();
            validateBlueprint();
            break;
        case 'r':
        case 'R': // R键 - 重新生成可解场景
            event.preventDefault();
            if (gameState.mode === 'puzzle') {
                generatePuzzleLevel();
            } else if (gameState.mode === 'tetris') {
                startTetrisLevel();
            } else if (gameState.mode === 'infinite') {
                startInfiniteLevel();
            } else {
                generateSolvableScenario();
            }
            updateMessage('已重新生成关卡！');
            break;
        case 'a':
        case 'A': // A键 - 分析蓝图并显示建议
            event.preventDefault();
            analyzeBlueprintAndShowSuggestions();
            break;
        case 's':
        case 'S': // S键 - 检查当前场景的可解性
            event.preventDefault();
            checkScenarioSolvability();
            break;
        case 'g':
        case 'G': // G键 - 生成建议的节点
            event.preventDefault();
            generateSuggestedNode();
            break;
        case 'c':
        case 'C': // C键 - 清空蓝图
            event.preventDefault();
            clearBlueprint();
            break;
        case 'h':
        case 'H': // H键 - 显示/隐藏解决方案提示
            event.preventDefault();
            toggleSolutionHints();
            gameState.levelConfig.levelProgress.hintsUsed++;
            break;
        case 'd':
        case 'D': // D键 - 显示调试信息
            event.preventDefault();
            showDebugInfo();
            break;
        case 'm':
        case 'M': // M键 - 切换游戏模式
            event.preventDefault();
            toggleGameMode();
            break;
        case 'n':
        case 'N': // N键 - 下一关（调试用）
            event.preventDefault();
            advanceToNextLevel();
            break;
        case 'p':
        case 'P': // P键 - 上一关（调试用）
            event.preventDefault();
            goToPreviousLevel();
            break;
        case 't':
        case 'T': // T键 - 切换到Tetris模式
            event.preventDefault();
            switchToTetrisMode();
            break;
        case 'q':
        case 'Q': // Q键 - 切换到Puzzle模式
            event.preventDefault();
            switchToPuzzleMode();
            break;
        case 'u':
        case 'U': // U键 - 切换多起点模式
            event.preventDefault();
            toggleMultipleStartNodes();
            break;
        case 'i':
        case 'I': // I键 - 切换多终点模式  
            event.preventDefault();
            toggleMultipleEndNodes();
            break;
        case 'x':
        case 'X': // X键 - 切换线路交叉限制
            event.preventDefault();
            toggleCrossingRestriction();
            break;
        case 'v':
        case 'V': // V键 - 切换交叉点显示（Shift+V检测所有交叉）
            event.preventDefault();
            if (event.shiftKey) {
                // Shift+V: 检测并显示所有交叉点
                const crossings = detectAllCrossings();
                updateMessage(`🔍 检测到 ${crossings.length} 个交叉点`);
            } else {
                // V: 切换交叉点显示
                toggleCrossPointDisplay();
            }
            break;
    }
}

// 切换游戏模式
function toggleGameMode() {
    if (gameState.mode === 'puzzle') {
        gameState.mode = 'tetris';
        updateMessage('🎮 切换到俄罗斯方块模式！');
        startTetrisLevel();
    } else if (gameState.mode === 'tetris') {
        gameState.mode = 'infinite';
        updateMessage('🌊 切换到无限构筑模式！');
        startInfiniteLevel();
    } else {
        gameState.mode = 'puzzle';
        updateMessage('🧩 切换到谜题模式！');
        generatePuzzleLevel();
    }
    updateLevelDisplay(); // 确保UI及时更新
}

// 切换到俄罗斯方块模式
function switchToTetrisMode() {
    gameState.mode = 'tetris';
    gameState.tetrisMode.isActive = false; // 先停止当前tetris
    updateMessage('🎮 进入俄罗斯方块模式！');
    startTetrisLevel();
    updateLevelDisplay();
}

// 切换到谜题模式
function switchToPuzzleMode() {
    gameState.mode = 'puzzle';
    gameState.tetrisMode.isActive = false; // 停止tetris
    gameState.infiniteMode.isActive = false; // 停止infinite
    updateMessage('🧩 进入谜题模式！');
    generatePuzzleLevel();
    updateLevelDisplay();
}

// 切换到无限构筑模式
function switchToInfiniteMode() {
    gameState.mode = 'infinite';
    gameState.tetrisMode.isActive = false; // 停止tetris
    gameState.infiniteMode.isActive = false; // 先停止当前infinite
    updateMessage('🌊 进入无限构筑模式！');
    startInfiniteLevel();
    updateLevelDisplay();
}

// 统一的模式切换函数
function switchMode(newMode) {
    if (gameState.mode === newMode) return; // 已经是该模式，无需切换
    
    // 停止所有活跃的模式
    gameState.tetrisMode.isActive = false;
    gameState.infiniteMode.isActive = false;
    
    // 切换到新模式
    gameState.mode = newMode;
    
    switch (newMode) {
        case 'puzzle':
            updateMessage('🧩 切换到谜题模式！');
            generatePuzzleLevel();
            break;
        case 'tetris':
            updateMessage('🎮 切换到俄罗斯方块模式！');
            startTetrisLevel();
            break;
        case 'infinite':
            updateMessage('🌊 切换到无限构筑模式！');
            startInfiniteLevel();
            break;
    }
    
    // 更新UI显示
    updateLevelDisplay();
    updateModeButtonStates();
}

// 更新模式按钮状态
function updateModeButtonStates() {
    const puzzleBtn = document.getElementById('puzzle-mode-btn');
    const tetrisBtn = document.getElementById('tetris-mode-btn');
    const infiniteBtn = document.getElementById('infinite-mode-btn');
    
    // 移除所有active类
    [puzzleBtn, tetrisBtn, infiniteBtn].forEach(btn => {
        if (btn) btn.classList.remove('active');
    });
    
    // 为当前模式添加active类
    switch (gameState.mode) {
        case 'puzzle':
            if (puzzleBtn) puzzleBtn.classList.add('active');
            break;
        case 'tetris':
            if (tetrisBtn) tetrisBtn.classList.add('active');
            break;
        case 'infinite':
            if (infiniteBtn) infiniteBtn.classList.add('active');
            break;
    }
}

// 上一关（调试用）
function goToPreviousLevel() {
    if (gameState.level > 1) {
        gameState.level--;
        gameState.levelConfig.currentLevelStartTime = Date.now();
        
        if (gameState.mode === 'puzzle') {
            generatePuzzleLevel();
        } else if (gameState.mode === 'tetris') {
            startTetrisLevel();
        }
        
        updateLevelDisplay();
        updateMessage(`🔙 回到关卡 ${gameState.level}`);
    } else {
        updateMessage('已经是第一关了！');
    }
}

// 分析蓝图并显示建议
function analyzeBlueprintAndShowSuggestions() {
    const analysis = analyzeBlueprintAndSuggestNodes();
    const progress = getSolutionProgress();
    
    let message = '🔍 蓝图分析结果:\n\n';
    
    // 显示解决方案进度
    if (gameState.currentSolution) {
        message += `📊 解决方案进度: ${progress.completed}/${progress.total} 完成\n`;
        progress.details.forEach((detail, index) => {
            const status = detail.completed ? '✅' : '❌';
            message += `${status} 链 ${index + 1}: ${detail.source} → ${detail.target} (${detail.steps}步)\n`;
        });
        message += '\n';
    }
    
    // 显示一般性分析
    if (analysis.canSolve) {
        message += '✅ 当前场景理论上可解！\n';
    } else {
        message += '❌ 当前场景可能无法解决\n';
    }
    
    if (analysis.missingConnections.length > 0) {
        message += `\n缺少连接类型: ${analysis.missingConnections.length} 个\n`;
        analysis.missingConnections.forEach(missing => {
            message += `- ${missing.type} (${missing.color})\n`;
        });
    }
    
    if (analysis.suggestedNodeTypes.length > 0) {
        message += `\n建议节点: ${analysis.suggestedNodeTypes.length} 个\n`;
        analysis.suggestedNodeTypes.slice(0, 3).forEach((suggestion, index) => {
            message += `${index + 1}. ${suggestion.description}\n`;
        });
    }
    
    message += '\n💡 提示: 按 H 键查看具体解决方案';
    
    console.log(message);
    updateMessage('详细分析完成！查看控制台获取信息');
}

// 检查场景可解性
function checkScenarioSolvability() {
    const isSolvable = verifyScenarioSolvability();
    const allNodesCount = gameState.placedNodes.length + gameState.temporaryNodes.length;
    
    if (isSolvable) {
        updateMessage(`✅ 场景可解！(总节点: ${allNodesCount})`);
    } else {
        updateMessage(`❌ 场景不可解 (总节点: ${allNodesCount})`);
        
        // 提供一些调试信息
        const startNodes = [...gameState.placedNodes, ...gameState.temporaryNodes].filter(node => node.type === 'start');
        const endNodes = [...gameState.placedNodes, ...gameState.temporaryNodes].filter(node => node.type === 'end');
        console.log('调试信息:', {
            startNodes: startNodes.length,
            endNodes: endNodes.length,
            normalNodes: allNodesCount - startNodes.length - endNodes.length,
            startOutputs: startNodes.map(n => n.outputPorts.map(p => `${p.type}(${p.color})`)),
            endInputs: endNodes.map(n => n.inputPorts.map(p => `${p.type}(${p.color})`))
        });
    }
}

// 生成建议的节点
function generateSuggestedNode() {
    const analysis = analyzeBlueprintAndSuggestNodes();
    
    if (analysis.suggestedNodeTypes.length === 0) {
        updateMessage('当前没有建议的节点类型');
        return;
    }
    
    // 选择第一个建议来生成节点
    const suggestion = analysis.suggestedNodeTypes[0];
    const node = new Node('normal');
    
    // 添加建议的输出端口
    if (suggestion.requiredOutputs) {
        suggestion.requiredOutputs.forEach(output => {
            node.addOutputPort(output.type, output.color);
        });
    }
    
    // 添加一些能接受现有输出的输入端口
    if (suggestion.requiredInputs && suggestion.requiredInputs.length > 0) {
        const randomInput = suggestion.requiredInputs[Math.floor(Math.random() * suggestion.requiredInputs.length)];
        node.addInputPort(randomInput.type, randomInput.color);
    }
    
    // 确保节点至少有一个输入和一个输出
    if (node.inputPorts.length === 0) {
        node.addInputPort('square', '#ff5252'); // 默认输入
    }
    if (node.outputPorts.length === 0) {
        node.addOutputPort('square', '#ff5252'); // 默认输出
    }
    
    // 放置在临时区域
    const tempY = gameState.temporaryArea.y + 10 + gameState.temporaryNodes.length * 100;
    node.moveTo(gameState.temporaryArea.x + 10, tempY);
    gameState.temporaryNodes.push(node);
    
    updateMessage(`已生成建议节点: ${suggestion.description.slice(0, 30)}...`);
}

// 切换解决方案提示显示
function toggleSolutionHints() {
    if (!gameState.currentSolution) {
        updateMessage('当前场景没有可用的解决方案信息');
        return;
    }
    
    gameState.solutionVisible = !gameState.solutionVisible;
    
    if (gameState.solutionVisible) {
        displaySolutionHints();
        updateMessage('🔍 解决方案提示已显示！再按 H 键隐藏');
    } else {
        updateMessage('解决方案提示已隐藏');
    }
}

// 显示解决方案提示
function displaySolutionHints() {
    if (!gameState.currentSolution) return;
    
    let hintMessage = '💡 解决方案提示:\n\n';
    
    gameState.currentSolution.forEach((chain, index) => {
        hintMessage += `🔗 转换链 ${index + 1}:\n`;
        hintMessage += `   源: ${chain.source.type}(${chain.source.color})\n`;
        hintMessage += `   目标: ${chain.target.type}(${chain.target.color})\n`;
        
        if (chain.steps.length > 0) {
            hintMessage += `   步骤:\n`;
            chain.steps.forEach((step, stepIndex) => {
                hintMessage += `   ${stepIndex + 1}. ${step.inputType}(${step.inputColor}) → ${step.outputType}(${step.outputColor})\n`;
            });
        } else {
            hintMessage += `   直接连接\n`;
        }
        hintMessage += '\n';
    });
    
    hintMessage += '💭 操作提示:\n';
    hintMessage += '1. 将临时区域的节点拖到摆放区域\n';
    hintMessage += '2. 按照转换链连接对应的端口\n';
    hintMessage += '3. 确保每个转换步骤都正确连接\n';
    
    console.log(hintMessage);
    
    // 在界面上显示简化的提示
    const simplifiedHint = `解决方案: ${gameState.currentSolution.length} 个转换链，详细信息请查看控制台`;
    updateMessage(simplifiedHint);
}

// 获取当前解决方案的进度信息
function getSolutionProgress() {
    if (!gameState.currentSolution) {
        return { completed: 0, total: 0, details: [] };
    }
    
    const progress = {
        completed: 0,
        total: gameState.currentSolution.length,
        details: []
    };
    
    gameState.currentSolution.forEach((chain, index) => {
        const isCompleted = validateTransformationChainExecution(chain);
        if (isCompleted) {
            progress.completed++;
        }
        
        progress.details.push({
            chainId: chain.id,
            completed: isCompleted,
            source: `${chain.source.type}(${chain.source.color})`,
            target: `${chain.target.type}(${chain.target.color})`,
            steps: chain.steps.length
        });
    });
    
    return progress;
}

// 显示调试信息
function showDebugInfo() {
    if (!gameState.currentSolution) {
        updateMessage('当前场景没有可用的调试信息');
        return;
    }
    
    let debugMessage = '🐛 调试信息:\n\n';
    
    // 场景概览
    debugMessage += '📊 场景概览:\n';
    const startNodes = gameState.placedNodes.filter(node => node.type === 'start');
    const endNodes = gameState.placedNodes.filter(node => node.type === 'end');
    const normalNodes = gameState.placedNodes.filter(node => node.type === 'normal');
    const tempNodes = gameState.temporaryNodes;
    
    debugMessage += `  起点节点: ${startNodes.length} 个\n`;
    debugMessage += `  终点节点: ${endNodes.length} 个\n`;
    debugMessage += `  已放置普通节点: ${normalNodes.length} 个\n`;
    debugMessage += `  临时区域节点: ${tempNodes.length} 个\n`;
    debugMessage += `  当前连接: ${gameState.connections.length} 个\n`;
    
    // 端口统计
    debugMessage += '\n🔌 端口统计:\n';
    let totalInputPorts = 0;
    let totalOutputPorts = 0;
    let connectedInputs = 0;
    let connectedOutputs = 0;
    
    [...gameState.placedNodes, ...gameState.temporaryNodes].forEach(node => {
        totalInputPorts += node.inputPorts.length;
        totalOutputPorts += node.outputPorts.length;
        
        node.inputPorts.forEach(port => {
            if (port.connectedTo) connectedInputs++;
        });
        
        node.outputPorts.forEach(port => {
            if (port.connectedTo) connectedOutputs++;
        });
    });
    
    debugMessage += `  总输入端口: ${totalInputPorts} (已连接: ${connectedInputs})\n`;
    debugMessage += `  总输出端口: ${totalOutputPorts} (已连接: ${connectedOutputs})\n`;
    debugMessage += `  连接使用率: ${Math.round((connectedInputs / Math.max(totalInputPorts, 1)) * 100)}%\n`;
    
    // 解决方案状态
    debugMessage += '\n✅ 解决方案状态:\n';
    const progress = getSolutionProgress();
    debugMessage += `  完成进度: ${progress.completed}/${progress.total} (${Math.round((progress.completed / Math.max(progress.total, 1)) * 100)}%)\n`;
    
    progress.details.forEach((detail, index) => {
        const status = detail.completed ? '✅' : '❌';
        debugMessage += `  ${status} 链 ${index + 1}: ${detail.source} → ${detail.target}\n`;
    });
    
    // 一对一约束检查
    debugMessage += '\n🔗 一对一约束检查:\n';
    const oneToOneValidation = validateRuntimeOneToOneConstraint();
    if (oneToOneValidation.errors.length === 0) {
        debugMessage += '  ✅ 没有发现违反一对一约束的连接\n';
    } else {
        debugMessage += `  ❌ 发现 ${oneToOneValidation.errors.length} 个违规:\n`;
        oneToOneValidation.errors.forEach(error => {
            debugMessage += `    - ${error}\n`;
        });
    }
    
    // 验证结果
    debugMessage += '\n🔍 验证结果:\n';
    const validation = performDetailedValidation();
    debugMessage += `  整体有效性: ${validation.isValid ? '✅ 有效' : '❌ 无效'}\n`;
    debugMessage += `  错误数量: ${validation.errors.length}\n`;
    debugMessage += `  警告数量: ${validation.warnings.length}\n`;
    
    if (validation.errors.length > 0) {
        debugMessage += '  错误详情:\n';
        validation.errors.forEach(error => {
            debugMessage += `    - ${error}\n`;
        });
    }
    
    debugMessage += '\n💡 提示: 查看浏览器控制台获取更多技术细节';
    
    console.log(debugMessage);
    updateMessage('调试信息已输出到控制台，按 F12 查看详细信息');
}

// 在指定位置查找端口
function findPortAtPosition(x, y, area = 'game') {
    const nodes = area === 'temp' ? gameState.temporaryNodes : gameState.placedNodes;
    for (const node of nodes) {
        for (const port of node.getAllPorts()) {
            if (Math.abs(port.x - x) < 10 && Math.abs(port.y - y) < 10) {
                return port;
            }
        }
    }
    return null;
}

// 在指定位置查找节点
function findNodeAtPosition(x, y, area = 'game') {
    const nodes = area === 'temp' ? gameState.temporaryNodes : gameState.placedNodes;
    for (const node of nodes) {
        if (x >= node.x && x <= node.x + node.width &&
            y >= node.y && y <= node.y + node.height) {
            return node;
        }
    }
    return null;
}

// 开始连接拖拽
function startConnectionDrag(port, x, y) {
    gameState.draggingConnection = {
        startPort: port,
        endX: x,
        endY: y,
        targetPort: null
    };
}

// 更新连接拖拽
function updateConnectionDrag(x, y) {
    if (!gameState.draggingConnection) return;
    
    gameState.draggingConnection.endX = x;
    gameState.draggingConnection.endY = y;
    
    // 检查是否悬停在有效端口上
    const targetPort = findPortAtPosition(x, y);
    if (targetPort && gameState.draggingConnection.startPort.canConnectTo(targetPort)) {
        gameState.draggingConnection.targetPort = targetPort;
    } else {
        gameState.draggingConnection.targetPort = null;
    }
}

// 完成连接拖拽
function finishConnectionDrag(x, y) {
    if (!gameState.draggingConnection) return;
    
    const { startPort, targetPort } = gameState.draggingConnection;
    
    if (targetPort) {
        // 创建新连接以检查交叉
        let fromPort, toPort;
        if (startPort.side === 'output') {
            fromPort = startPort;
            toPort = targetPort;
        } else {
            fromPort = targetPort;
            toPort = startPort;
        }
        
        const tempConnection = new Connection(fromPort, toPort);
        
        // 检查线路交叉
        const crossingResult = checkConnectionCrossing(tempConnection);
        
        if (!gameState.crossingDetection.allowCrossing && crossingResult.crosses) {
            // 不允许交叉，阻止连接
            updateMessage(`❌ 连接被阻止：线路不能交叉！发现 ${crossingResult.crossPoints.length} 个交叉点`);
            
            // 可选：高亮显示冲突的连接
            highlightCrossingConnections(crossingResult.crossingConnections);
            
        } else {
            // 允许创建连接
            
            // 断开目标端口的现有连接
            if (targetPort.connectedTo) {
                const existingConnection = gameState.connections.find(conn => 
                    conn.fromPort.id === targetPort.connectedTo || conn.toPort.id === targetPort.connectedTo
                );
                if (existingConnection) {
                    existingConnection.disconnect();
                }
            }
            
            // 断开起始端口的现有连接
            if (startPort.connectedTo) {
                const existingConnection = gameState.connections.find(conn => 
                    conn.fromPort.id === startPort.connectedTo || conn.toPort.id === startPort.connectedTo
                );
                if (existingConnection) {
                    existingConnection.disconnect();
                }
            }
            
            // 创建新连接
            gameState.connections.push(tempConnection);
            
            if (crossingResult.crosses) {
                updateMessage(`⚠️ 连接已创建，但存在 ${crossingResult.crossPoints.length} 个交叉点`);
            } else {
                updateMessage('✅ 连接已建立！');
            }
            
            // 更新交叉点检测
            detectAllCrossings();
        }
    }
    
    gameState.draggingConnection = null;
}

// 开始节点拖拽 - 修正版本，确保正确的偏移计算
function startNodeDrag(node, x, y, currentArea = null) {
    // 确定节点当前所在区域
    const nodeArea = gameState.temporaryNodes.includes(node) ? 'temporary' : 'placement';

    // 计算鼠标相对于节点的偏移（使用相同坐标系）
    let offsetX, offsetY;

    if (currentArea === 'temp' && nodeArea === 'temporary') {
        // 鼠标和节点都在临时区域，直接计算偏移
        offsetX = x - node.x;
        offsetY = y - node.y;
    } else if (currentArea !== 'temp' && nodeArea === 'placement') {
        // 鼠标和节点都在摆放区域，直接计算偏移
        offsetX = x - node.x;
        offsetY = y - node.y;
    } else {
        // 跨区域情况，需要转换坐标系
        const globalCoords = getGlobalCoordinates(x, y, currentArea);
        const nodeGlobalX = nodeArea === 'temporary' ? node.x : node.x + gameState.temporaryArea.width;
        const nodeGlobalY = node.y;
        offsetX = globalCoords.x - nodeGlobalX;
        offsetY = globalCoords.y - nodeGlobalY;
    }

    gameState.draggingNode = {
        node: node,
        offsetX: offsetX,
        offsetY: offsetY,
        originalArea: nodeArea,
        originalX: node.x,
        originalY: node.y,
        dragStartTime: Date.now(),
        isVisible: true,
        lastValidX: node.x,
        lastValidY: node.y
    };

    gameState.selectedNode = node;

    // 添加拖拽样式
    node.isDragging = true;
}

// 更新节点拖拽 - 修正版本，确保正确的位置计算
function updateNodeDrag(x, y, currentArea = null) {
    if (!gameState.draggingNode) return;

    const node = gameState.draggingNode.node;

    // 计算新位置（考虑拖拽偏移）
    const newX = x - gameState.draggingNode.offsetX;
    const newY = y - gameState.draggingNode.offsetY;

    // 更新节点位置到鼠标位置减去偏移
    node.moveTo(newX, newY);
    gameState.draggingNode.isVisible = true;

    // 获取全局坐标用于区域判断
    const globalCoords = getGlobalCoordinates(x, y, currentArea);
    const targetArea = determineTargetArea(globalCoords.x, globalCoords.y);

    // 更新视觉反馈
    updateDragPreview(targetArea);

    // 更新最后有效位置（使用局部坐标）
    if (targetArea && isValidPosition(newX, newY, targetArea)) {
        gameState.draggingNode.lastValidX = newX;
        gameState.draggingNode.lastValidY = newY;
    }
}

// 完成节点拖拽 - 修正版本，确保正确的最终位置
function finishNodeDrag(x, y, currentArea = null) {
    if (!gameState.draggingNode) return;

    const node = gameState.draggingNode.node;
    const originalArea = gameState.draggingNode.originalArea;

    // 清除拖拽样式
    node.isDragging = false;

    // 计算最终位置（考虑拖拽偏移）
    const finalX = x - gameState.draggingNode.offsetX;
    const finalY = y - gameState.draggingNode.offsetY;

    // 获取全局坐标并确定目标区域
    const globalCoords = getGlobalCoordinates(x, y, currentArea);
    const targetArea = determineTargetArea(globalCoords.x, globalCoords.y);

    // 清除拖拽预览
    clearDragPreview();
    
    // 处理区域转换
    if (targetArea === 'temporary' && originalArea === 'placement') {
        // 从摆放区移回临时区
        const index = gameState.placedNodes.indexOf(node);
        if (index !== -1) {
            gameState.placedNodes.splice(index, 1);
            gameState.temporaryNodes.push(node);

            // 使用最终位置，转换为临时区域坐标
            const localCoords = globalToLocalCoordinates(globalCoords.x, globalCoords.y, 'temporary');
            const adjustedX = localCoords.x - gameState.draggingNode.offsetX;
            const adjustedY = localCoords.y - gameState.draggingNode.offsetY;
            const validX = Math.max(10, Math.min(adjustedX, gameState.temporaryArea.width - node.width - 10));
            const validY = Math.max(10, Math.min(adjustedY, gameState.temporaryArea.height - node.height - 10));
            node.moveTo(validX, validY);

            updateMessage('📦 节点已移回临时区域！');

            // 播放移动动画
            playMoveAnimation(node, 'temporary');
        }
    } else if (targetArea === 'placement' && originalArea === 'temporary') {
        // 从临时区移到摆放区
        const index = gameState.temporaryNodes.indexOf(node);
        if (index !== -1) {
            gameState.temporaryNodes.splice(index, 1);
            gameState.placedNodes.push(node);

            // 使用最终位置，转换为摆放区域坐标
            const localCoords = globalToLocalCoordinates(globalCoords.x, globalCoords.y, 'placement');
            const adjustedX = localCoords.x - gameState.draggingNode.offsetX;
            const adjustedY = localCoords.y - gameState.draggingNode.offsetY;
            const validX = Math.max(10, Math.min(adjustedX, gameState.placementArea.width - node.width - 10));
            const validY = Math.max(10, Math.min(adjustedY, gameState.placementArea.height - node.height - 10));
            node.moveTo(validX, validY);

            updateMessage('🎯 节点已移动到摆放区域！');

            // 播放移动动画
            playMoveAnimation(node, 'placement');

            // 在Tetris模式下，移除节点后检查胜利条件
            if (gameState.mode === 'tetris' && gameState.tetrisMode.isActive) {
                // 重置溢出警告计数（因为玩家成功移动了节点）
                if (gameState.tetrisMode.overflowWarnings > 0) {
                    gameState.tetrisMode.overflowWarnings = Math.max(0, gameState.tetrisMode.overflowWarnings - 1);
                }
                setTimeout(() => checkTetrisVictoryCondition(), 100);
            }
        }
    } else if (targetArea === originalArea && targetArea !== null) {
        // 在同一区域内移动 - 使用精确的最终位置
        let maxWidth, maxHeight;
        if (targetArea === 'temporary') {
            maxWidth = gameState.temporaryArea.width;
            maxHeight = gameState.temporaryArea.height;
        } else {
            maxWidth = gameState.placementArea.width;
            maxHeight = gameState.placementArea.height;
        }

        // 使用计算好的最终位置，确保在边界内
        const validX = Math.max(10, Math.min(finalX, maxWidth - node.width - 10));
        const validY = Math.max(10, Math.min(finalY, maxHeight - node.height - 10));
        node.moveTo(validX, validY);
        
    } else {
        // 拖拽到无效区域，恢复原位置
        updateMessage('❌ 无效的放置位置！节点已恢复到原位置');
        node.moveTo(gameState.draggingNode.originalX, gameState.draggingNode.originalY);
        
        // 播放错误动画
        playErrorAnimation();
    }
    
    gameState.draggingNode = null;
}

// 删除节点
function deleteNode(node) {
    // 删除所有相关连接
    const relatedConnections = gameState.connections.filter(conn => 
        conn.fromPort.nodeId === node.id || conn.toPort.nodeId === node.id
    );
    
    relatedConnections.forEach(conn => conn.disconnect());
    
    // 从相应数组中删除节点
    let index = gameState.temporaryNodes.indexOf(node);
    if (index !== -1) {
        gameState.temporaryNodes.splice(index, 1);
    }
    
    index = gameState.placedNodes.indexOf(node);
    if (index !== -1) {
        gameState.placedNodes.splice(index, 1);
    }
    
    gameState.selectedNode = null;
    updateMessage('节点已删除！');
}

// 清空蓝图
function clearBlueprint() {
    // 只清空摆放区域的普通节点，保留起点和终点
    const nodesToRemove = gameState.placedNodes.filter(node => 
        node.type !== 'start' && node.type !== 'end'
    );
    
    nodesToRemove.forEach(node => deleteNode(node));
    
    updateMessage('蓝图已清空！');
}

// 渲染游戏
function render() {
    // 渲染临时区域
    if (gameState.temporaryArea.canvas && gameState.temporaryArea.ctx) {
        const tempCtx = gameState.temporaryArea.ctx;
        const tempCanvas = gameState.temporaryArea.canvas;
        
        // 清空临时区域画布
        tempCtx.clearRect(0, 0, tempCanvas.width, tempCanvas.height);
        
        // 绘制临时区域背景
        let tempBgColor = 'rgba(255, 87, 87, 0.05)';
        
        // 如果正在拖拽且目标是临时区域，高亮显示
        if (gameState.dragPreview && gameState.dragPreview.targetArea === 'temporary' && gameState.dragPreview.showHighlight) {
            tempBgColor = 'rgba(255, 87, 87, 0.15)';
            // 绘制边框高亮
            tempCtx.strokeStyle = '#ff5252';
            tempCtx.lineWidth = 3;
            tempCtx.setLineDash([5, 5]);
            tempCtx.strokeRect(2, 2, tempCanvas.width - 4, tempCanvas.height - 4);
            tempCtx.setLineDash([]);
        }
        
        tempCtx.fillStyle = tempBgColor;
        tempCtx.fillRect(0, 0, tempCanvas.width, tempCanvas.height);
        
        // 绘制临时区域边框
        tempCtx.strokeStyle = '#ff5252';
        tempCtx.lineWidth = 2;
        tempCtx.strokeRect(0, 0, tempCanvas.width, tempCanvas.height);
        
        // 绘制临时区域标题
        tempCtx.fillStyle = '#ff5252';
        tempCtx.font = 'bold 14px Arial';
        tempCtx.textAlign = 'center';
        tempCtx.fillText('临时区域', tempCanvas.width / 2, 20);
        
        // 显示容量信息
        const currentCount = gameState.temporaryNodes.length;
        const maxCount = gameState.temporaryArea.maxNodes;
        tempCtx.font = '12px Arial';
        tempCtx.fillText(`${currentCount}/${maxCount}`, tempCanvas.width / 2, tempCanvas.height - 10);
        
        // 绘制临时区域的节点
        for (const node of gameState.temporaryNodes) {
            drawNode(tempCtx, node);
        }
    }
    
    // 渲染摆放区域
    if (gameState.placementArea.canvas && gameState.placementArea.ctx) {
        const gameCtx = gameState.placementArea.ctx;
        const gameCanvas = gameState.placementArea.canvas;
        
        // 清空摆放区域画布
        gameCtx.clearRect(0, 0, gameCanvas.width, gameCanvas.height);
        
        // 绘制摆放区域背景
        let placementBgColor = 'rgba(33, 150, 243, 0.05)';
        
        // 如果正在拖拽且目标是摆放区域，高亮显示
        if (gameState.dragPreview && gameState.dragPreview.targetArea === 'placement' && gameState.dragPreview.showHighlight) {
            placementBgColor = 'rgba(33, 150, 243, 0.15)';
            // 绘制边框高亮
            gameCtx.strokeStyle = '#2196F3';
            gameCtx.lineWidth = 3;
            gameCtx.setLineDash([5, 5]);
            gameCtx.strokeRect(2, 2, gameCanvas.width - 4, gameCanvas.height - 4);
            gameCtx.setLineDash([]);
        }
        
        gameCtx.fillStyle = placementBgColor;
        gameCtx.fillRect(0, 0, gameCanvas.width, gameCanvas.height);
        
        // 绘制摆放区域边框
        gameCtx.strokeStyle = '#2196F3';
        gameCtx.lineWidth = 2;
        gameCtx.strokeRect(0, 0, gameCanvas.width, gameCanvas.height);
        
        // 绘制摆放区域标题
        gameCtx.fillStyle = '#2196F3';
        gameCtx.font = 'bold 14px Arial';
        gameCtx.textAlign = 'center';
        gameCtx.fillText('摆放区域', gameCanvas.width / 2, 20);
        
        // 绘制连接线
        drawConnections(gameCtx);
        
        // 绘制交叉点
        if (gameState.crossingDetection.showCrossPoints) {
            drawCrossPoints(gameCtx);
        }
        
        // 绘制摆放区域的节点
        for (const node of gameState.placedNodes) {
            drawNode(gameCtx, node);
        }
        
        // 绘制拖拽中的连接线
        if (gameState.draggingConnection) {
            drawDraggingConnection(gameCtx);
        }
    }
}

// 绘制区域边界
function drawAreas(ctx) {
    ctx.strokeStyle = '#ccc';
    ctx.lineWidth = 2;
    
    // 临时区域
    ctx.strokeRect(
        gameState.temporaryArea.x, 
        gameState.temporaryArea.y, 
        gameState.temporaryArea.width, 
        gameState.temporaryArea.height
    );
    
    // 摆放区域
    ctx.strokeRect(
        gameState.placementArea.x, 
        gameState.placementArea.y, 
        gameState.placementArea.width, 
        gameState.placementArea.height
    );
    
    // 区域标签
    ctx.fillStyle = '#666';
    ctx.font = '14px Arial';
    ctx.fillText('临时区域', gameState.temporaryArea.x + 5, gameState.temporaryArea.y + 20);
    ctx.fillText('摆放区域', gameState.placementArea.x + 5, gameState.placementArea.y + 20);
}

// 绘制连接线
function drawConnections(ctx) {
    for (const connection of gameState.connections) {
        const startPoint = connection.getStartPoint();
        const endPoint = connection.getEndPoint();
        
        // 根据连接状态设置样式
        if (connection.highlighted) {
            // 高亮显示冲突的连接
            ctx.strokeStyle = '#ff0000';
            ctx.lineWidth = 5;
            ctx.setLineDash([10, 5]);
        } else {
            ctx.strokeStyle = connection.color;
            ctx.lineWidth = 3;
            ctx.setLineDash([]);
        }
        
        // 绘制贝塞尔曲线
        ctx.beginPath();
        ctx.moveTo(startPoint.x, startPoint.y);
        
        const controlPoint1X = startPoint.x + 100;
        const controlPoint1Y = startPoint.y;
        const controlPoint2X = endPoint.x - 100;
        const controlPoint2Y = endPoint.y;
        
        ctx.bezierCurveTo(controlPoint1X, controlPoint1Y, controlPoint2X, controlPoint2Y, endPoint.x, endPoint.y);
        ctx.stroke();
        ctx.setLineDash([]);
    }
    
    // 绘制交叉点
    if (gameState.crossingDetection.showCrossPoints) {
        drawCrossPoints(ctx);
    }
}

// 绘制交叉点
function drawCrossPoints(ctx) {
    const crossPoints = gameState.crossingDetection.crossPoints;
    
    for (const crossData of crossPoints) {
        const { x, y } = crossData.point;
        
        // 绘制交叉点标记
        ctx.fillStyle = '#ff6b35';
        ctx.strokeStyle = '#ffffff';
        ctx.lineWidth = 2;
        
        // 绘制圆形标记
        ctx.beginPath();
        ctx.arc(x, y, 8, 0, 2 * Math.PI);
        ctx.fill();
        ctx.stroke();
        
        // 绘制X标记
        ctx.strokeStyle = '#ffffff';
        ctx.lineWidth = 3;
        ctx.beginPath();
        ctx.moveTo(x - 5, y - 5);
        ctx.lineTo(x + 5, y + 5);
        ctx.moveTo(x + 5, y - 5);
        ctx.lineTo(x - 5, y + 5);
        ctx.stroke();
        
        // 可选：绘制警告文字
        if (gameState.crossingDetection.crossPoints.length <= 5) {
            ctx.fillStyle = '#333333';
            ctx.font = '12px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('⚠️', x, y - 15);
        }
    }
}





// 获取节点颜色
function getNodeColor(node) {
    switch(node.type) {
        case 'start': return '#4CAF50';
        case 'end': return '#f44336';
        default: return '#2196F3';
    }
}

// 绘制端口
function drawPort(ctx, port) {
    ctx.fillStyle = port.color;
    ctx.strokeStyle = '#333';
    ctx.lineWidth = 1;
    
    const size = 12;
    const x = port.x - size / 2;
    const y = port.y - size / 2;
    
    // 根据端口类型绘制不同形状
    switch(port.type) {
        case 'square':
            ctx.fillRect(x, y, size, size);
            ctx.strokeRect(x, y, size, size);
            break;
        case 'circle':
            ctx.beginPath();
            ctx.arc(port.x, port.y, size / 2, 0, 2 * Math.PI);
            ctx.fill();
            ctx.stroke();
            break;
        case 'triangle':
            ctx.beginPath();
            ctx.moveTo(port.x, y);
            ctx.lineTo(x, y + size);
            ctx.lineTo(x + size, y + size);
            ctx.closePath();
            ctx.fill();
            ctx.stroke();
            break;
        case 'diamond':
            ctx.beginPath();
            ctx.moveTo(port.x, y);
            ctx.lineTo(x + size, port.y);
            ctx.lineTo(port.x, y + size);
            ctx.lineTo(x, port.y);
            ctx.closePath();
            ctx.fill();
            ctx.stroke();
            break;
    }
}

// 绘制拖拽中的连接线
function drawDraggingConnection(ctx) {
    const { startPort, endX, endY, targetPort } = gameState.draggingConnection;
    
    ctx.strokeStyle = targetPort ? '#4CAF50' : '#ff9800';
    ctx.lineWidth = 3;
    
    ctx.beginPath();
    ctx.moveTo(startPort.x, startPort.y);
    
    const controlPoint1X = startPort.x + 100;
    const controlPoint1Y = startPort.y;
    const controlPoint2X = endX - 100;
    const controlPoint2Y = endY;
    
    ctx.bezierCurveTo(controlPoint1X, controlPoint1Y, controlPoint2X, controlPoint2Y, endX, endY);
    ctx.stroke();
}

// ======================================
// 线路交叉检测算法
// ======================================

// 检查两条线段是否相交
function doLinesIntersect(line1, line2) {
    const { x1: a1, y1: a2, x2: a3, y2: a4 } = line1;
    const { x1: b1, y1: b2, x2: b3, y2: b4 } = line2;
    
    // 计算方向
    function orientation(p, q, r) {
        const val = (q.y - p.y) * (r.x - q.x) - (q.x - p.x) * (r.y - q.y);
        if (val === 0) return 0;  // 共线
        return (val > 0) ? 1 : 2; // 顺时针或逆时针
    }
    
    // 检查点q是否在线段pr上
    function onSegment(p, q, r) {
        return q.x <= Math.max(p.x, r.x) && q.x >= Math.min(p.x, r.x) &&
               q.y <= Math.max(p.y, r.y) && q.y >= Math.min(p.y, r.y);
    }
    
    const p1 = { x: a1, y: a2 };
    const q1 = { x: a3, y: a4 };
    const p2 = { x: b1, y: b2 };
    const q2 = { x: b3, y: b4 };
    
    const o1 = orientation(p1, q1, p2);
    const o2 = orientation(p1, q1, q2);
    const o3 = orientation(p2, q2, p1);
    const o4 = orientation(p2, q2, q1);
    
    // 一般情况
    if (o1 !== o2 && o3 !== o4) return true;
    
    // 特殊情况：点共线且在线段上
    if (o1 === 0 && onSegment(p1, p2, q1)) return true;
    if (o2 === 0 && onSegment(p1, q2, q1)) return true;
    if (o3 === 0 && onSegment(p2, p1, q2)) return true;
    if (o4 === 0 && onSegment(p2, q1, q2)) return true;
    
    return false;
}

// 计算两条线段的交点
function getLineIntersection(line1, line2) {
    const { x1: x1, y1: y1, x2: x2, y2: y2 } = line1;
    const { x1: x3, y1: y3, x2: x4, y2: y4 } = line2;
    
    const denom = (x1 - x2) * (y3 - y4) - (y1 - y2) * (x3 - x4);
    if (Math.abs(denom) < 1e-10) return null; // 平行线
    
    const t = ((x1 - x3) * (y3 - y4) - (y1 - y3) * (x3 - x4)) / denom;
    const u = -((x1 - x2) * (y1 - y3) - (y1 - y2) * (x1 - x3)) / denom;
    
    if (t >= 0 && t <= 1 && u >= 0 && u <= 1) {
        return {
            x: x1 + t * (x2 - x1),
            y: y1 + t * (y2 - y1)
        };
    }
    
    return null;
}

// 检查新连接是否与现有连接交叉
function checkConnectionCrossing(newConnection) {
    if (!gameState.crossingDetection.enabled) return { crosses: false, crossPoints: [] };
    
    const newLine = getConnectionLine(newConnection);
    const crossPoints = [];
    const crossingConnections = [];
    
    for (const existingConnection of gameState.connections) {
        if (existingConnection === newConnection) continue;
        
        const existingLine = getConnectionLine(existingConnection);
        
        if (doLinesIntersect(newLine, existingLine)) {
            const intersection = getLineIntersection(newLine, existingLine);
            if (intersection) {
                crossPoints.push(intersection);
                crossingConnections.push(existingConnection);
            }
        }
    }
    
    return { 
        crosses: crossPoints.length > 0, 
        crossPoints: crossPoints,
        crossingConnections: crossingConnections
    };
}

// 从连接对象获取线段信息
function getConnectionLine(connection) {
    const startPoint = connection.getStartPoint();
    const endPoint = connection.getEndPoint();
    
    return {
        x1: startPoint.x,
        y1: startPoint.y,
        x2: endPoint.x,
        y2: endPoint.y
    };
}

// 检查所有现有连接的交叉情况
function detectAllCrossings() {
    const crossPoints = [];
    const connections = gameState.connections;
    
    for (let i = 0; i < connections.length; i++) {
        for (let j = i + 1; j < connections.length; j++) {
            const line1 = getConnectionLine(connections[i]);
            const line2 = getConnectionLine(connections[j]);
            
            if (doLinesIntersect(line1, line2)) {
                const intersection = getLineIntersection(line1, line2);
                if (intersection) {
                    crossPoints.push({
                        point: intersection,
                        connection1: connections[i],
                        connection2: connections[j]
                    });
                }
            }
        }
    }
    
    gameState.crossingDetection.crossPoints = crossPoints;
    return crossPoints;
}

// 高亮显示冲突的连接
function highlightCrossingConnections(connections) {
    // 重置所有连接的高亮状态
    gameState.connections.forEach(conn => {
        conn.highlighted = false;
    });
    
    // 高亮冲突的连接
    connections.forEach(conn => {
        conn.highlighted = true;
    });
    
    // 2秒后取消高亮
    setTimeout(() => {
        gameState.connections.forEach(conn => {
            conn.highlighted = false;
        });
    }, 2000);
}

// 切换交叉点显示
function toggleCrossPointDisplay() {
    gameState.crossingDetection.showCrossPoints = !gameState.crossingDetection.showCrossPoints;
    
    if (gameState.crossingDetection.showCrossPoints) {
        detectAllCrossings();
        updateMessage(`✅ 交叉点显示已开启 (发现 ${gameState.crossingDetection.crossPoints.length} 个交叉点)`);
    } else {
        updateMessage('❌ 交叉点显示已关闭');
    }
}

// 切换线路交叉限制
function toggleCrossingRestriction() {
    gameState.crossingDetection.allowCrossing = !gameState.crossingDetection.allowCrossing;
    
    if (gameState.crossingDetection.allowCrossing) {
        updateMessage('⚠️ 线路交叉限制已关闭');
    } else {
        updateMessage('🚫 线路交叉限制已开启');
    }
}

// 更新交叉检测按钮状态
function updateCrossingButtonStates() {
    const crossingBtn = document.getElementById('crossing-toggle-btn');
    const crossPointBtn = document.getElementById('crosspoint-toggle-btn');
    
    if (crossingBtn) {
        if (gameState.crossingDetection.allowCrossing) {
            crossingBtn.classList.remove('active');
            crossingBtn.innerHTML = '✅ 允许交叉 <kbd>X</kbd>';
        } else {
            crossingBtn.classList.add('active');
            crossingBtn.innerHTML = '🚫 交叉限制 <kbd>X</kbd>';
        }
    }
    
    if (crossPointBtn) {
        if (gameState.crossingDetection.showCrossPoints) {
            crossPointBtn.classList.add('active');
            crossPointBtn.innerHTML = '👁️ 隐藏交叉点 <kbd>V</kbd>';
        } else {
            crossPointBtn.classList.remove('active');
            crossPointBtn.innerHTML = '👁️ 显示交叉点 <kbd>V</kbd>';
        }
    }
}

// ===== 跨区域拖拽辅助函数 =====

// 获取全局坐标（将局部坐标转换为全局坐标）
function getGlobalCoordinates(x, y, currentArea) {
    if (currentArea === 'temp') {
        // 临时区域坐标已经是全局坐标系中的临时区域坐标
        return { x: x, y: y };
    } else {
        // 摆放区域需要加上临时区域的宽度偏移
        return { x: x + gameState.temporaryArea.width, y: y };
    }
}

// 将全局坐标转换为局部坐标
function globalToLocalCoordinates(globalX, globalY, targetArea) {
    if (targetArea === 'temporary') {
        return { x: globalX, y: globalY };
    } else if (targetArea === 'placement') {
        return { x: globalX - gameState.temporaryArea.width, y: globalY };
    }
    return { x: globalX, y: globalY };
}

// 确定目标区域
function determineTargetArea(globalX, globalY) {
    // 检查是否在临时区域
    if (globalX >= 0 && globalX <= gameState.temporaryArea.width && 
        globalY >= 0 && globalY <= gameState.temporaryArea.height) {
        return 'temporary';
    }
    
    // 检查是否在摆放区域
    const placementStartX = gameState.temporaryArea.width;
    if (globalX >= placementStartX && globalX <= placementStartX + gameState.placementArea.width &&
        globalY >= 0 && globalY <= gameState.placementArea.height) {
        return 'placement';
    }
    
    return null; // 无效区域
}

// 检查位置是否有效
function isValidPosition(x, y, area) {
    if (!area) return false;
    
    if (area === 'temporary') {
        return x >= 10 && x <= gameState.temporaryArea.width - 10 &&
               y >= 10 && y <= gameState.temporaryArea.height - 10;
    } else if (area === 'placement') {
        return x >= 10 && x <= gameState.placementArea.width - 10 &&
               y >= 10 && y <= gameState.placementArea.height - 10;
    }
    
    return false;
}

// 更新拖拽预览（显示目标区域高亮）
function updateDragPreview(targetArea) {
    // 清除之前的预览状态
    gameState.dragPreview = {
        targetArea: targetArea,
        showHighlight: targetArea !== null
    };
}

// 清除拖拽预览
function clearDragPreview() {
    gameState.dragPreview = null;
}

// 播放移动动画
function playMoveAnimation(node, targetArea) {
    const duration = 300; // 300ms动画
    const startTime = Date.now();
    
    // 添加移动动画状态
    node.moveAnimation = {
        isPlaying: true,
        startTime: startTime,
        duration: duration,
        targetArea: targetArea
    };
    
    // 播放简单的闪烁效果
    let blinkCount = 0;
    const blinkInterval = setInterval(() => {
        node.highlighted = !node.highlighted;
        blinkCount++;
        
        if (blinkCount >= 4) { // 闪烁2次
            clearInterval(blinkInterval);
            node.highlighted = false;
            node.moveAnimation = null;
        }
    }, 150);
}

// 改进的绘制节点函数，支持拖拽状态渲染
function drawNode(ctx, node) {
    // 如果节点正在拖拽，增加视觉效果
    if (node.isDragging) {
        // 绘制阴影
        ctx.save();
        ctx.shadowColor = 'rgba(0, 0, 0, 0.3)';
        ctx.shadowBlur = 10;
        ctx.shadowOffsetX = 2;
        ctx.shadowOffsetY = 2;
    }
    
    // 原有的节点绘制逻辑
    ctx.fillStyle = getNodeColor(node);
    if (node.highlighted || (gameState.selectedNode === node)) {
        ctx.strokeStyle = '#ffeb3b';
        ctx.lineWidth = 3;
    } else {
        ctx.strokeStyle = '#666';
        ctx.lineWidth = 1;
    }
    
    // 绘制节点主体
    ctx.fillRect(node.x, node.y, node.width, node.height);
    ctx.strokeRect(node.x, node.y, node.width, node.height);
    
    // 如果是拖拽状态，添加半透明效果
    if (node.isDragging) {
        ctx.fillStyle = 'rgba(255, 255, 255, 0.2)';
        ctx.fillRect(node.x, node.y, node.width, node.height);
        ctx.restore();
    }
    
    // 绘制节点标签
    ctx.fillStyle = '#000';
    ctx.font = '12px Arial';
    ctx.textAlign = 'center';
    ctx.fillText(node.label, node.x + node.width/2, node.y + node.height/2 + 4);
    
    // 绘制端口
    node.getAllPorts().forEach(port => {
        drawPort(ctx, port);
    });
}

// 启动游戏
document.addEventListener('DOMContentLoaded', () => {
    initGame();
    
    // 启动渲染循环
    function gameLoop() {
        render();
        
        // 在Tetris模式下更新UI
        if (gameState.mode === 'tetris' && gameState.tetrisMode.isActive) {
            updateTetrisInfo();
        }
        
        // 在Infinite模式下更新UI
        if (gameState.mode === 'infinite' && gameState.infiniteMode.isActive) {
            updateInfiniteInfo();
        }
        
        requestAnimationFrame(gameLoop);
    }
    gameLoop();
}); 